"""<PERSON><PERSON><PERSON><PERSON> para fazer notificações ao Google Chat."""
import logging
import os
import requests
import pandas as pd

from src.connections.connections import Connections

logger = logging.getLogger("conversas_logger")

CONNECTIONS = Connections.get_instance()

GCP_BIGQUERY_DATASET = os.getenv("GCP_BIGQUERY_DATASET")
DB_SCHEMA = os.getenv("DB_SCHEMA", "public")
BUCKET_NAME_CAMPANHA = os.getenv("BUCKET_NAME_CAMPANHA", "image_campanha")
if DB_SCHEMA == "development":
    BUCKET_NAME_CAMPANHA += "_dev"

CHATS_URL = 'https://chat.googleapis.com/v1/spaces/AAAAgHbY370/messages'
CHATS_KEY = 'AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI'
CHATS_TOKEN = 'M8sGocV66HnZR0m71nGvD2GxGbJHbR_ichiFOqIiBPc'
KEVIO_ID  = "users/111028092658508152627"
MATEUS_ID = "users/110843554708463589529"

QUERY = f"""
SELECT
  creation_time,
  user_email,
  job_type,
  statement_type,
  query,
  total_bytes_processed,
  MAX(stage.records_written) AS max_records_written,
  referenced_tables.table_id
FROM
  `conversas-ai.region-southamerica-east1.INFORMATION_SCHEMA.JOBS` AS JOBS
CROSS JOIN
  UNNEST(JOBS.job_stages) AS stage
CROSS JOIN
  UNNEST(JOBS.referenced_tables) AS referenced_tables
WHERE
  JOBS.destination_table.dataset_id = '{DB_SCHEMA}'
  AND (JOBS.statement_type IN ('DELETE',
      'MERGE',
      'TRUNCATE_TABLE',
      'DROP_TABLE',
      'DROP_SNAPSHOT_TABLE',
      'ALTER_TABLE')
    AND stage.records_written > 5)
  AND JOBS.creation_time BETWEEN CURRENT_TIMESTAMP() - INTERVAL 1 day
  AND CURRENT_TIMESTAMP()
GROUP BY
  1,
  2,
  3,
  4,
  5,
  6,
  8;
"""

def get_data() -> pd.DataFrame:
    """Obtém os dados da query do Postgres."""
    # pylint: disable=protected-access
    df = pd.read_sql_query(
        QUERY,
        CONNECTIONS.postgres_connection,
    )

    return df

def build_notification_message():
    """
    Constrói uma mensagem de notificação resumida, agrupando por tabela, email e tipo de operação.
    """
    data = get_data()

    if data.empty:
        return None

    message = (
        "🚨 *Resumo de Operações Problemáticas Executadas Ontem * 🚨\n\n"
        "Seguem abaixo as operações detectadas, agrupadas por tabela, email e tipo de operação:\n\n"
    )

    # Identifica as tabelas distintas
    tables = data['table_id'].unique()

    for table in tables:
        # Filtra apenas as linhas relativas a essa tabela
        data_table = data[data['table_id'] == table]

        # Agrupa por 'user_email' E 'statement_type' para exibir resumo
        # Assim teremos estatísticas por usuário e tipo de operação
        summary = (
            data_table
            .groupby(['user_email', 'statement_type'])
            .agg(
                qtd_operacoes=('creation_time', 'count'),
                bytes_processados=('total_bytes_processed', 'sum'),
                linhas_escritas=('max_records_written', 'sum')
            )
            .reset_index()
        )

        # Cabeçalho para cada tabela
        message += f"*Tabela*: `{table}`\n"

        # Podemos iterar diretamente nas linhas de summary
        for _, row in summary.iterrows():
            message += (
                f"• *Usuário*: `{row['user_email']}`\n"
                f"  - *Tipo de operação*: `{row['statement_type']}`\n"
                f"  - *Quantidade de operações*: {row['qtd_operacoes']}\n"
                f"  - *Bytes processados (total)*: {row['bytes_processados']}\n"
                f"  - *Linhas escritas (total)*: {row['linhas_escritas']}\n\n"
            )

        message += "-------------------------------------------\n\n"

    message += (
        f"Por favor, <{MATEUS_ID}> e <{KEVIO_ID}>, "
        "revisem as *queries* acima e verifiquem se as operações eram "
        "realmente necessárias. 🕵️‍♂️"
    )

    return message

def send_notification():
    """Envia uma notificação ao Google Chat."""
    message = build_notification_message()

    if not message:
        return

    headers = {
        'Content-Type': 'application/json; charset=UTF-8',
    }

    params = {
        'key': CHATS_KEY,
        'token': CHATS_TOKEN
    }

    payload = {
        "text": message
    }

    logger.info(payload)
    response = requests.post(
        CHATS_URL,
        headers=headers,
        params=params,
        json=payload,
        timeout=10
        )

    if response.status_code != 200:
        try:
            logger.info(response.text)
        except requests.exceptions.HTTPError:
            for i, part in (message.split("*Tabela*")):
                data = {
                    "text": part if i == 0 else f"Tabela*{part}"
                }
                response = requests.post(
                    CHATS_URL,
                    headers=headers,
                    params=params,
                    json=data,
                    timeout=10
                    )
    else:
        logger.info("Notificação enviada com sucesso!")
