#FIXME(refactor): identificar o que precisa ainda ser refatorado por aqui.

import logging
import json
import time
from datetime import datetime, timedelta
import os
from typing import Optional, Dict
from requests.exceptions import ConnectTimeout, JSONDecodeError


from src.connections.delayed_queue import DelayedQueue
from src.worker.messager_modules.whatsapp_messager.whatsapp_messager import WhatsappMessager
from src.extras.config import Config
from src.data.bigquery_data import BigQueryData as bq
from src.extras.util import parse_phone, retry, determine_voice, identify_pendency, log_func_name
from src.data.data_processor import DataProcessor as dp
from src.data.bigquery_data import get_from_instance, is_rede as _is_rede
from src.worker.tts_modules.openai_tts.openai_tts_module import convert_to_audio
from src.connections.connections import Connections
from src.integrations.pacto.tools.integration_tools import PactoIntegrationTools

from src.routerllm.routerllm_response_module import RouterLLMResponseModule
from src.worker.tracing import tracer, extract, inject

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")

connections = Connections.get_instance()
delayed_queue = DelayedQueue()

voices = { "feminino": "nova", "masculino": "onyx" }

SECONDS_TO_WAIT_TILL_RESPONSE = int(os.getenv("SECONDS_TO_WAIT_TILL_RESPONSE", '5'))
GCP_BIGQUERY_DATASET = os.getenv("GCP_BIGQUERY_DATASET")
DB_SCHEMA = os.getenv("DB_SCHEMA", "public")
BUCKET_NAME_CAMPANHA = os.getenv("BUCKET_NAME_CAMPANHA", "image_campanha")
if DB_SCHEMA == "development":
    BUCKET_NAME_CAMPANHA += "_dev"

@log_func_name
def log_response(response: dict) -> None:
    logger.info(f" [*] [messages_received_worker] Response: {response}")
    connections.redis_client.lpush('logs', json.dumps(response))

@log_func_name
def retry_send_message(original_task: dict):
    """
    Re-envia a mensagem por meio da fila `messages_received`.
    """
    if (retries := original_task.get('retries', 0) > 0):
        logger.info(" [*] [messages_received_worker] Retrying message again.")
        original_task['retries'] = retries + 1
        logger.info(original_task)
    else:
        original_task['retries'] = 1
    original_task['data']['text'] = {
        'message': "INFORMAÇÃO DO SISTEMA: O usuário não respondeu à sua mensagem, " + \
            "por favor, fale educadamente com ele novamente."
    }

    context = original_task.get('context', {})
    with tracer.start_as_current_span("process_retry", context=extract(context)) as span:
        span.set_attribute("id_empresa", original_task["id_empresa"])
        span.set_attribute("phone_number", original_task["data"]["phone"])
        span.set_attribute("model_source", "")
        span.set_attribute("origin", "")
        carrier = {}
        inject(carrier)
        original_task['context'] = carrier
        router_received_message(original_task)

@log_func_name
def set_retry_contact(telefone, id_empresa, original_task):
    """
    Estabelece um novo contato com o usuário X horas após o último contato.

    Args:
        telefone (str): Número de telefone do usuário.
        id_empresa (str): ID da empresa.
        original_task (dict): Tarefa original.
    """
    retry_contact_time = int(os.getenv("RETRY_CONTACT_TIME", '10'))
    logger.info(" [*] [messages_received_worker] Setting retry contact for %s", telefone)
    try:
        task_id = delayed_queue.delay_call(
            func=retry_send_message,
            args={"original_task": original_task},
            minutes=retry_contact_time
        )
        logger.info(" [*] [messages_received_worker] Task ID: %s", task_id)
        connections.redis_client.set(f"retry_contact:{telefone}-{id_empresa}", task_id, ex=2*60*60)
    except Exception as e:
        logger.error(" [*] [messages_received_worker] Error to set retry contact: %s", e)

@log_func_name
def unset_retry_contact(telefone, id_empresa):
    """
    Remove a tarefa de recontato agendada para o usuário.

    Args:
        telefone (str): Número de telefone do usuário.
        id_empresa (str): ID da empresa.
    """
    logger.info(" [*] [messages_received_worker] Unsetting retry contact for %s", telefone)
    task_id = connections.redis_client.get(f"retry_contact:{telefone}-{id_empresa}")
    if task_id:
        task_id = task_id.decode('utf-8')
        delayed_queue.cancel_task(task_id)
        connections.redis_client.delete(f"retry_contact:{telefone}-{id_empresa}")

@log_func_name
def handle_retry_message(
        task: dict,
        telefone_envio,
        id_empresa,
        bq_: bq,
        llm: RouterLLMResponseModule,
        messager: WhatsappMessager
        ) -> Optional[Dict]:
    """
    Gerencia o reenvio de mensagens.

    Args:
        task (dict): Tarefa original.
        telefone_envio (str): Número de telefone do usuário.
        id_empresa (str): ID da empresa.
        llm (LLMSelector): Instância do seletor de modelos de linguagem.
    """
    print(json.dumps(task, indent=4))
    if 'retries' in task:
        retries = int(task['retries'])
        if retries == 1:
            unset_retry_contact(telefone_envio, id_empresa)
            set_retry_contact(telefone_envio, id_empresa, task)
        else:
            llm.end_conversation()
            messager.end_conversation(
                phone=telefone_envio,
                id_empresa=id_empresa,
                sticker=bq_.get_sticker()
                )
            unset_retry_contact(telefone_envio, id_empresa)
            return {
                "data": {
                    "status": "success",
                    "phone": telefone_envio,
                    "response": "Conversa finalizada",
                    "id_empresa": id_empresa,
                    "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                },
                "table" : "mensagens_enviadas"
            }
    else:
        unset_retry_contact(telefone_envio, id_empresa)
        set_retry_contact(telefone_envio, id_empresa, task)

    return None

@log_func_name
def z_api_first_message(telefone: str, id_empresa: str, fase_crm: str, redis_client, nome="DESCONHECIDO", id_matriz: str = None, is_rede: bool = False):
    user_data = {
        "aluno": {
            "codigo": None,
            "pessoa": {
                "nome": f"{nome}",
                "telefonesconsulta": f"{telefone}",
            },
            "situacao": f"LEAD",
            "fase_crm": f"{fase_crm}",
        },
        "fase_atual": fase_crm
    }

    bq_ = bq(id_empresa=id_empresa)

    if is_rede:
        rede_json = bq(id_empresa=id_matriz).get_chain_context(extra_query=f"OR id_empresa = '{id_matriz}'")
        lista_empresas = [id_matriz] + [rede.get("id_empresa") for rede in rede_json] if rede_json else []
    else: 
        logger.info(f" [*] [messages_received_worker] zapi first message: id_empresa: {id_empresa}")
        lista_empresas = [id_empresa]

    logger.info(f" [*] [messages_received_worker] Lista de empresas: {lista_empresas}")

    user_found, response = False, {}
    for id_empresa_ in lista_empresas:
        logger.info(f" [*] [messages_received_worker] Buscando usuário no Sistema Pacto com o id_empresa: {id_empresa_}")
        pit = PactoIntegrationTools(id_empresa_, id_matriz)
        try:
            user_found, response = pit.search_by_phone(phone=telefone.removeprefix("+55"))
        except (ConnectTimeout, JSONDecodeError):
            logger.error(f" [*] [messages_received_worker] Timeout ao tentar buscar usuário no Sistema Pacto")
            continue
        if user_found:
            logger.info(" [*] [z_api_first] Usuário encontrado no Sistema Pacto pelo telefone")
            if isinstance(response, dict):
                user_data = json.dumps(response)
            elif isinstance(response, str):
                user_data["aluno"]["pessoa"]["nome"] = response
                user_data = json.dumps(user_data)

            # Isso necessário só quando é uma rede, pois nesse caso o bq é instanciado id_empresa = None.
            # Se entrou aqui quer dizer que achei o usuário pelo telefone, então eu preciso instanciar o bq com o id_empresa correta.
            # Se não for uma rede, o codigo abaixo é redundante, mas não é um problema.
            id_empresa = id_empresa_
            bq_.set_id_empresa(id_empresa)
            break
    if not user_found:
        logger.info(" [*] [z_api_first] Usuário não encontrado no Sistema Pacto pelo telefone")
        user_data = json.dumps(user_data)
        redis_client.set(f"{telefone}-waiting_cpf", "True", ex=86400)

    logger.info(f" [*] [messages_received_worker] Usuário encontrado no Z-API: {json.dumps(user_data, indent=2, ensure_ascii=False)}")
    bq_.save_message("user", "Oi", telefone)
    data = {
        "type": "user",
        "id_empresa": id_empresa,
        "id_matriz": id_matriz,
        "data":
            {
            "telefone": telefone,
            "contexto": user_data,
            "fase": fase_crm
            }
    }
    redis_client.lpush('task_queue_bd_contexts', json.dumps(data))
    redis_client.set(
        f"{telefone}-{id_empresa}",
        json.dumps(user_data),
        ex=8*60*60
    )

@log_func_name
def can_send_audio(text, last_message_type):
    """Verifica se a mensagem pode ser enviada como áudio"""
    patterns = ["http://", "https://", "www.", "@"]
    no_links = not any([pattern in str(text) for pattern in patterns]) # se pelo menos um dos padrões estiver presente, não envie áudio
    
    is_last_msg_audio = last_message_type == "audio" # olha se a última mensagem foi um áudio

    #logger.info(f"\nHá Links no texto? {no_links}\nÚltima mensagem foi Audio? {is_last_msg_audio}\nO texto é curto? {short_text}\nO tamanho original do texto é {len(text)}\nTamanho depois de limpar: {text_size}")
    return no_links and is_last_msg_audio

@log_func_name
def check_strikes(telefone, id_empresa, redis_client, bq_: bq) -> bool:
    strikes = redis_client.get(f"strikes:{telefone}-{id_empresa}")
    if strikes:
        user_strikes = int(json.loads(strikes).get('user_strikes', 0))
        registered = bool(json.loads(strikes).get('registered', False))
        logger.info(f"Strikes: {strikes}")
        if user_strikes >= 3:
            if not registered:
                QUERY = f"INSERT INTO crm_ia.strikes_usuarios (telefone, id_empresa, data_registro) VALUES ('{telefone}', '{id_empresa}', '{datetime.now().strftime('%Y-%m-%dT%H:%M:%S')}')"
                bq_._save_dataframe_to_bq(QUERY)
                redis_client.set(f"strikes:{telefone}-{id_empresa}", json.dumps({"user_strikes": user_strikes, "registered": True}), ex=24*60*60)
            return True
    return False

@log_func_name
def check_pendency(telefone, id_empresa, original_task):
    logger.info(f" [*] [messages_received_worker] Checking pendency for {telefone}")
    bq_ = bq(id_empresa=id_empresa)
    has_pendency, response = identify_pendency(id_empresa, telefone, bq_)
    if has_pendency:
        logger.info(f" [*] [messages_received_worker] Pendency found for {telefone}")
        original_task['data']['text']['message'] = f"INFORMAÇÃO DO SISTEMA: {response}"
        original_task['data']['pendency_processed'] = True
        connections.redis_client.lpush('messages_received', json.dumps(original_task))
    
    context = original_task.get('context', {})
    with tracer.start_as_current_span("process_pendency", context=extract(context)) as span:
        span.set_attribute("id_empresa", id_empresa)
        span.set_attribute("phone_number", original_task["data"]["phone"])
        span.set_attribute("model_source", "routerllm")
        span.set_attribute("origin", "z_api")
        carrier = {}
        inject(carrier)
        original_task['context'] = carrier
        router_received_message(original_task)


def set_pendency_verification(telefone, id_empresa, original_task):
    pendency_verification_time = int(os.getenv("PENDENCY_VERIFICATION_TIME", 5))
    logger.info(f" [*] [messages_received_worker] Setting pendency verification for {telefone}")
    task_id = delayed_queue.delay_call(
        func=check_pendency,
        args={"telefone": telefone, "id_empresa": id_empresa, "original_task": original_task},
        minutes=pendency_verification_time
    )
    connections.redis_client.set(f"pendency_verification:{telefone}-{id_empresa}", task_id, ex=24*60*60)

@log_func_name
def unset_pendency_verification(telefone, id_empresa):
    logger.info(f" [*] [messages_received_worker] Unsetting pendency verification for {telefone}")
    task_id = connections.redis_client.get(f"pendency_verification:{telefone}-{id_empresa}")
    if task_id:
        task_id = task_id.decode('utf-8')
        delayed_queue.cancel_task(task_id)
        connections.redis_client.delete(f"pendency_verification:{telefone}-{id_empresa}")

@log_func_name
def ignore_message(mommment):
    """Verifica se a mensagem deve ser ignorada"""
    ignore = False
    # moment will be something like 1726855579062
    time_since = datetime.now() - datetime.fromtimestamp(mommment/1000)
    if time_since.total_seconds() > 60*60*5:
        ignore = True
    return ignore

@retry(retries=3, delay=2, backoff=2, status_codes=(500,))
def handle_received_message(task):
    # Obter a data e hora atual no formato ISO 8601
    current_time_user = datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
    
    redis_client = connections.redis_client
    id_empresa = task['id_empresa']
    canAnswer = task['canAnswer']
    situacao = (
        data.get("aluno", {}).get("situacao", {}).get("codigo") or
        data.get("aluno", {}).get("pessoa", {}).get("situacao") or
        data.get("aluno", {}).get("situacao") or
        None
    )
    data = task['data']
    origin = task.get('origin', 'z_api')
    data = json.loads(json.dumps(data))
    #logger.info(f'MENSAGEM RECEBIDA:\n{json.dumps(data, indent=4)}')
    logger.info(f'ID_EMPRESA: {id_empresa}')

    if data.get('chave_empresa', None) is None:
        instance_id = data.get('instanceId')
        logger.info(f'INSTANCE_ID: {instance_id}')
        connected_phone = parse_phone(data.get('connectedPhone'))
        id_empresa, _ = get_from_instance(instance_id)
        if id_empresa is None:
            logger.info("id_empresa nâo foi encontrado!!!")
            return {
                "data":{
                    "status": "error",
                    "phone": data.get('phone', data.get('telefone', None)),
                    "response": "Empresa não encontrada",
                    "id_empresa": None,
                    "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                },
                "table" : "mensagens_enviadas"
            }
        is_group = bool(data.get('isGroup', False))
        logger.info("Processando a mensagem")
        user_info_msg, content = dp.process_message(data)
        user_msg_type = user_info_msg.get('type', None)
        logger.info(f"Menssagem processada! tipo: {user_msg_type}")
        message_id = data.get('messageId', None)
        id_session = data.get('sessionId', None)

        match user_msg_type:
            case "audio":
                user_response = str(content)
            case "text":
                user_response = content.get("message")
            case "notification":
                return {"success": "No response needed"}
            case None:
                user_response = "O usuário te mandou um tipo de mídia não suportado"
            case _:
                user_response = f"O usuário te mandou uma mensagem do tipo {user_msg_type}, esse é o conteúdo: {json.dumps(content)}"
    
    else:
        logger.info("Chave empresa no request não é nulo!")
        id_empresa = data.get('chave_empresa', None)
        telefone = data.get('telefone', None)
        user_response = data.get('mensagem', None)
        is_group = False

    if is_group:
        logger.info("Se trata de um grupo!")
        telefone = parse_phone(data.get('participantPhone', None))
        telefone_envio = data.get('phone', None)

    elif not is_group:
        logger.info("Não é um grupo!")
        telefone = parse_phone(data.get('phone', data.get('telefone', None)))
        telefone_envio = telefone
    
    logger.info("Conectou no BQ!")
    bq_ = bq(id_empresa=id_empresa)

    if not canAnswer:
        logger.info(f"LLM não pode responder!")
        user_context, id_contexto, id_empresa = bq_.get_user_context(telefone=telefone)
        bq_.save_message(
            "user",
            user_response,
            telefone,
            message_type=user_msg_type,
            n_chars=len(user_response),
            n_seconds=user_info_msg.get('n_seconds', 0),
            message_id=message_id,
            provider=origin,
            id_contexto=id_contexto,
            situacao=situacao
        )
        if user_context and  'aluno' in user_context and 'codigo' in user_context['aluno']:
            if user_context['aluno']['codigo']:
                user_task = {"id_empresa":id_empresa,
                            "message":f"USER: {user_response}",
                            "cliente": user_context['aluno']['codigo'],
                            "tipoContato": "WA",
                            "fase": id_contexto,
                            "data": current_time_user}
                redis_client.lpush('task_queue_crm_msg_history', json.dumps(user_task))

        return log_response({
            "data": {
                "status": "success",
                "phone": telefone_envio,
                "response": "IA nào pode responder, pois o usuário está em outro departamento",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
            "table" : "mensagens_enviadas"
        })

    if check_strikes(telefone, id_empresa, redis_client, bq_):
        logger.info("Usuário Não pode falar porque levou 3 strikes!")
        return {
            "data":{
                "status": "error",
                "phone": telefone_envio,
                "response": "Usuário bloqueado por comportamento malicioso.",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
            "table" : "mensagens_enviadas"
        }

    if ignore_message(data.get('momment')):
        logger.info("Mensagem ignorada porque já muito antiga!")
        return {
            "data":{
                "status": "error",
                "phone": telefone_envio,
                "response": "Mensagem muito antiga.",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
            "table" : "mensagens_enviadas"
        }

    user_data = bq_.get_user_context(telefone=telefone)
    logger.info("Pegou contexto do usuário")

    if not user_data[0]:
        logger.info("Contexto do usuário é nulo")
        nome = data.get('senderName', 'DESCONHECIDO')
        logger.info("Manda para primeira mensagem")
        z_api_first_message(telefone, id_empresa, "LEADS_HOJE", redis_client, nome)
        logger.info("Vamos tentar pegar o contexto denovo")
        user_data = bq_.get_user_context(telefone=telefone)
    
    logger.info(f"user_data: {json.dumps(user_data, indent=2, ensure_ascii=False)}")
    
    user_context = user_data[0]
    id_contexto = user_data[1]

    llm = RouterLLMResponseModule(
        user_context=user_context, 
        id_empresa=id_empresa, 
        phase=id_contexto, 
        telefone=telefone, 
        is_group=is_group, 
        id_session=id_session, 
        origin=origin
    )

    messager = WhatsappMessager(origin, bq=bq_)
    is_retry = task.get('retries', 0) > 0
    if (retry_response := handle_retry_message(
            task,
            telefone_envio,
            id_empresa,
            bq_,
            llm,
            messager
            )) is not None:
        return retry_response
    
    logger.info("obetendo resposta do llm")

    llm_response, llm_infos = "", {}
    logger.info(f"[AQUI] Can answer? {canAnswer}")

    llm_response, llm_infos = llm.get_response(user_response)

    if llm_response == "FINISH":
        messager.end_conversation(
            phone=telefone_envio,
            id_empresa=id_empresa,
            sticker=bq_.get_sticker()
        )
        unset_retry_contact(telefone_envio, id_empresa)
        return {
            "data": {
                "status": "success",
                "phone": telefone_envio,
                "response": "Conversa finalizada",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
        }
    logger.info("Resposta do llm gerada")
    logger.info(f"Resposta LLM:\n{llm_response}")

    send_audio = can_send_audio(text=llm_response, last_message_type=user_msg_type)
    logger.info(f"Vai ser em áudio a resposta? {send_audio}")
    message_type = "text"
    message_data = llm_response

    if canAnswer and send_audio:
        retry = False
        old_llm_response, old_llm_infos = "", {}
        if llm_infos["n_chars"] > 500: # se a resposta for muito longa, tenta gerar uma resposta mais curta
            old_llm_response, old_llm_infos = llm_response, llm_infos.copy()
            logger.info("gerando nova resposta, pois a última foi muito grande")
            llm_response, llm_infos = llm.get_response(user_response + "(Responda de forma mais breve e resumida)")
            retry = True

        voice = determine_voice(id_empresa=id_empresa)
        logger.info(f" [*] [messages_received_worker] Determined voice: {voice}")
        message_data, tts_seconds = convert_to_audio(llm_response, voice = voice) # audio em binário
        message_type = "audio"
    else:
        retry_audio = False
        old_llm_response, old_llm_infos = "", {}
        tts_seconds = 0

    logger.info("Mandando para messager enviar para a pessoa")

    sent_message_id = messager.send_message(
        message_data=message_data,
        phone=telefone_envio,
        flow=Config.FLOW_NAME,
        id_session=id_session,
        id_empresa=id_empresa,
        message_type=message_type,
        message_id=message_id,
        is_group=is_group,
        connected_phone=connected_phone
    )

    
    if is_retry:
        if not os.getenv('RETRY_MESSAGE', 'false') == 'true':
            return {
                "data": {
                    "status": "error",
                    "phone": telefone_envio,
                    "response": (
                        "INFORMAÇÃO DO SISTEMA: O usuário não respondeu à sua mensagem, "
                        "por favor, fale educadamente com ele novamente."
                    ),
                    "id_empresa": id_empresa,
                    "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                },
                "table" : "mensagens_enviadas"
            }

    logger.info("Salvando o resultado")
    bq_.save_message(
        "user",
        user_response,
        telefone,
        message_type=user_msg_type,
        n_chars=len(user_response),
        n_seconds=user_info_msg.get('n_seconds', 0),
        message_id=message_id,
        provider=messager.provider,
        id_contexto=id_contexto,
        situacao=situacao
    )

    if canAnswer and send_audio and llm_infos is not None:
        if retry and old_llm_infos is not None:
            bq_.save_message(
                "assistant", 
                old_llm_response, 
                telefone, 
                message_type="text", 
                prompt_tokens=old_llm_infos["prompt_tokens"], 
                completion_tokens=old_llm_infos["completion_tokens"], 
                n_chars=old_llm_infos["n_chars"], 
                model=llm_infos["model"], 
                provider=messager.provider,
                message_id=sent_message_id
            )
        bq_.save_message(
                "assistant", 
                llm_response, 
                telefone, 
                message_type="audio", 
                prompt_tokens=llm_infos["prompt_tokens"], 
                completion_tokens=llm_infos["completion_tokens"], 
                n_chars=llm_infos["n_chars"], 
                n_seconds=tts_seconds, 
                model=llm_infos["model"],
                provider=messager.provider,
                message_id=sent_message_id
            )
    elif canAnswer and llm_infos is not None:
        bq_.save_message(
                "assistant", 
                llm_response, 
                telefone, 
                message_type="text", 
                prompt_tokens=llm_infos["prompt_tokens"], 
                completion_tokens=llm_infos["completion_tokens"], 
                n_chars=llm_infos["n_chars"], 
                model=llm_infos["model"],
                provider=messager.provider,
                message_id=sent_message_id
            )

    if user_context and 'aluno' in user_context and 'codigo' in user_context['aluno']:
        if user_context['aluno']['codigo']:
            user_task = {"id_empresa":id_empresa,
                        "message":f"USER: {user_response}",
                        "cliente": user_context['aluno']['codigo'],
                        "tipoContato": "WA",
                        "fase": id_contexto,
                        "data": current_time_user}
            redis_client.lpush('task_queue_crm_msg_history', json.dumps(user_task))
            
            current_time = datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            
            if canAnswer: 
                redis_client.lpush('task_queue_crm_msg_history',
                            json.dumps({"id_empresa":id_empresa,
                                        "message":f"IA: {llm_response}",
                                        "cliente": user_context['aluno']['codigo'],
                                        "tipoContato": "WA",
                                        "fase": id_contexto,
                                        "data": current_time}))
        else:
            logger.info(" [*] [messages_received_worker] No aluno code found")
    else:
        logger.info(" [*] [messages_received_worker] No aluno found")

    unset_pendency_verification(telefone, id_empresa)
    if not data.get('pendency_processed', False):
        set_pendency_verification(telefone, id_empresa, task, bq_)

    return log_response({
        "data": {
            "status": "success",
            "phone": telefone_envio,
            "response": llm_response,
            "id_empresa": id_empresa,
            "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
        },
        "table" : "mensagens_enviadas"
        })

def handle_received_message_rede(task):
    # Obter a data e hora atual no formato ISO 8601
    current_time_user = datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
    redis_client = connections.redis_client
    
    id_empresa = task['id_empresa']
    canAnswer = task['canAnswer']
    situacao = (
        data.get("aluno", {}).get("situacao", {}).get("codigo") or
        data.get("aluno", {}).get("pessoa", {}).get("situacao") or
        data.get("aluno", {}).get("situacao") or
        None
    )
    data = task['data']
    origin = task.get('origin', 'z_api')
    data = json.loads(json.dumps(data))
    #logger.info(f'MENSAGEM RECEBIDA:\n{json.dumps(data, indent=4)}')
    logger.info(f'ID_EMPRESA: {id_empresa}')

    if data.get('chave_empresa', None) is None:
        instance_id = data.get('instanceId')
        connected_phone = parse_phone(data.get('connectedPhone'))
        id_matriz = task['id_matriz']

        logger.info(f'ID_MATRIZ: {id_matriz}')
        logger.info(f'INSTANCE_ID: {instance_id}')
        logger.info(f'MENSAGEM RECEBIDA:\n{json.dumps(data, indent=4)}')

        if id_matriz is None:
            return {
                "data":{
                    "status": "error",
                    "phone": data.get('phone', data.get('telefone', None)),
                    "response": "Empresa não encontrada",
                    "id_empresa": None,
                    "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                },
                "table" : "mensagens_enviadas"
            }
        is_group = bool(data.get('isGroup', False))
        user_info_msg, content = dp.process_message(data)
        user_msg_type = user_info_msg.get('type', None)
        message_id = data.get('messageId', None)

        match user_msg_type:
            case "audio":
                user_response = str(content)
            case "text":
                user_response = content.get("message")
            case "notification":
                return {"success": "No response needed"}
            case None:
                user_response = "O usuário te mandou um tipo de mídia não suportado"
            case _:
                user_response = f"O usuário te mandou uma mídia de {user_msg_type}, esse é o conteúdo: {json.dumps(content)}"
    else:
        id_matriz = data.get('chave_empresa', None)
        telefone = data.get('telefone', None)
        user_response = data.get('mensagem', None)
        is_group = False

    if is_group:
        telefone = parse_phone(data.get('participantPhone', None))
        telefone_envio = data.get('phone', None)

    elif not is_group:
        telefone = parse_phone(data.get('phone', data.get('telefone', None)))
        telefone_envio = telefone
    
    bq_ = bq(id_empresa=None, id_matriz=id_matriz)
    if not canAnswer:
        logger.info(f"LLM não pode responder!")
        user_context, id_contexto, id_empresa = bq_.get_user_context(telefone, use_id_matriz=True, use_id_empresa=False)
        bq_.save_message(
            "user",
            user_response,
            telefone,
            message_type=user_msg_type,
            n_chars=len(user_response),
            n_seconds=user_info_msg.get('n_seconds', 0),
            message_id=message_id,
            provider=origin,
            id_contexto=id_contexto,
            situacao=situacao
        )
        if user_context and 'aluno' in user_context and 'codigo' in user_context['aluno']:
            if user_context['aluno']['codigo']:
                user_task = {"id_empresa":id_empresa,
                            "message":f"USER: {user_response}",
                            "cliente": user_context['aluno']['codigo'],
                            "tipoContato": "WA",
                            "fase": id_contexto,
                            "data": current_time_user}
                redis_client.lpush('task_queue_crm_msg_history', json.dumps(user_task))

        return log_response({
            "data": {
                "status": "success",
                "phone": telefone_envio,
                "response": "IA nào pode responder, pois o usuário está em outro departamento",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
            "table" : "mensagens_enviadas"
        })
    user_context, fase_atual, id_empresa = bq_.get_user_context(telefone, use_id_matriz=True, use_id_empresa=False)
    
    id_empresa = id_empresa if id_empresa else id_matriz
    bq_.set_id_empresa(id_empresa)

    if not user_context:
        nome = data.get('senderName', 'DESCONHECIDO')
        z_api_first_message(
            telefone=telefone, 
            id_empresa=id_empresa, 
            id_matriz=id_matriz, 
            is_rede=True,
            fase_crm="LEADS_HOJE", 
            redis_client=redis_client, 
            nome=nome
        )

        # Motivo desse time.sleep é o que o z_api_first_message vai salvar o contexto do usuário no BQ,
        # Mas, antes que essa de "salvar" aconteça, o código já vai tentar pegar o contexto do usuário e, por consequência, vai voltar nulo.
        # Com o time.sleep, dou tempo para o BQ salvar o contexto do usuário. 5 segundo parece ser mais que o suficiente. Podemos tentar baixar isso
        time.sleep(5)
        user_context, fase_atual, id_empresa = bq_.get_user_context(telefone=telefone, use_id_matriz=True, use_id_empresa=False)

    logger.info(f" [*] [messages_received_worker] user_context: {json.dumps(user_context, indent=2, ensure_ascii=False)}")
    logger.info(f" [*] [messages_received_worker] id_empresa: {id_empresa}")

    if check_strikes(telefone, id_empresa, redis_client, bq_):
        return {
            "data":{
                "status": "error",
                "phone": telefone_envio,
                "response": "Usuário bloqueado por comportamento malicioso.",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
            "table" : "mensagens_enviadas"
        }

    if ignore_message(data.get('momment')):
        return {
            "data":{
                "status": "error",
                "phone": telefone_envio,
                "response": "Mensagem muito antiga.",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
            "table" : "mensagens_enviadas"
        }

    llm = RouterLLMResponseModule(
        user_context=user_context, 
        id_empresa=id_empresa, 
        phase=fase_atual, 
        telefone=telefone,
        is_group=is_group,
        id_matriz=id_matriz,
        is_rede=True
    )

    # Essa linha a baixo é necessária porque um aluno LEAD pode ter escolhido
    # uma academia da rede nessa exata interação com a IA. Nessa situação, a partir daqui
    # o bq já vai salvar as mensagens com a empresa escolhida pelo aluno.
    bq_.set_id_empresa(llm.get_id_empresa())
    messager = WhatsappMessager(origin, bq=bq_)
    is_retry = task.get('retries', 0) > 0
    if (retry_response := handle_retry_message(
            task,
            telefone_envio,
            id_empresa,
            bq_,
            llm,
            messager
            )) is not None:
        return retry_response
    

    llm_response, llm_infos = llm.get_response(user_response)

    if llm_response == "FINISH":
        messager.end_conversation(
            phone=telefone_envio,
            id_empresa=id_empresa,
            sticker=bq_.get_sticker()
        )
        unset_retry_contact(telefone_envio, id_empresa)
        return {
            "data": {
                "status": "success",
                "phone": telefone_envio,
                "response": "Conversa finalizada",
                "id_empresa": id_empresa,
                "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            },
        }
    logger.info("Resposta do llm gerada")
    logger.info(f"Resposta LLM:\n{llm_response}")


    send_audio = can_send_audio(text=llm_response, last_message_type=user_msg_type)
    message_type = "text"
    message_data = llm_response

    if send_audio:
        retry = False
        old_llm_response, old_llm_infos = "", {}
        if llm_infos["n_chars"] > 500: # se a resposta for muito longa, tenta gerar uma resposta mais curta
            old_llm_response, old_llm_infos = llm_response, llm_infos.copy()
            llm_response, llm_infos = llm.get_response(user_response + "(Responda de forma mais breve e resumida)")
            retry = True

        voice = determine_voice(id_empresa=id_empresa)
        logger.info(f" [*] [messages_received_worker] Determined voice: {voice}")
        message_data, tts_seconds = convert_to_audio(llm_response, voice = voice) # audio em binário
        message_type = "audio"

    #logger.info(f" [*] [messages_received_worker] message_data: {message_data}")
    
    sent_message_id = messager.send_message(
        message_data=message_data, 
        phone=telefone_envio, 
        flow=Config.FLOW_NAME, 
        id_empresa=id_empresa, 
        message_type=message_type, 
        message_id=message_id
    )

    if is_retry:
        if not os.getenv('RETRY_MESSAGE', 'false') == 'true':
            return {
                "data": {
                    "status": "error",
                    "phone": telefone_envio,
                    "response": (
                        "INFORMAÇÃO DO SISTEMA: O usuário não respondeu à sua mensagem, "
                        "por favor, fale educadamente com ele novamente."
                    ),
                    "id_empresa": id_empresa,
                    "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                },
                "table" : "mensagens_enviadas"
            }

    bq_.save_message(
        "user",
        user_response,
        telefone,
        message_type=user_msg_type,
        n_chars=len(user_response),
        n_seconds=user_info_msg.get('n_seconds', 0),
        message_id=message_id,
        provider=messager.provider,
        id_contexto=id_contexto,
        situacao=situacao
    )

    if send_audio and llm_infos is not None:
        if retry and old_llm_infos is not None:
            bq_.save_message(
                "assistant",
                old_llm_response,
                telefone,
                message_type="text",
                prompt_tokens=old_llm_infos["prompt_tokens"],
                completion_tokens=old_llm_infos["completion_tokens"],
                n_chars=old_llm_infos["n_chars"],
                model=llm_infos["model"],
                provider=messager.provider,
                message_id=sent_message_id
            )
        bq_.save_message(
                "assistant",
                llm_response,
                telefone,
                message_type="audio",
                prompt_tokens=llm_infos["prompt_tokens"],
                completion_tokens=llm_infos["completion_tokens"],
                n_chars=llm_infos["n_chars"],
                n_seconds=tts_seconds,
                model=llm_infos["model"],
                provider=messager.provider,
                message_id=sent_message_id
            )
    elif llm_infos is not None:
        bq_.save_message(
                "assistant",
                llm_response,
                telefone,
                message_type="text",
                prompt_tokens=llm_infos["prompt_tokens"],
                completion_tokens=llm_infos["completion_tokens"],
                n_chars=llm_infos["n_chars"],
                model=llm_infos["model"],
                provider=messager.provider,
                message_id=sent_message_id
            )

    if 'aluno' in user_context and 'codigo' in user_context['aluno']:
        if user_context['aluno']['codigo']:
            user_task = {"id_empresa":id_empresa,
                        "message":f"USER: {user_response}",
                        "cliente": user_context['aluno']['codigo'],
                        "tipoContato": "WA",
                        "fase": fase_atual,
                        "data": current_time_user}
            redis_client.lpush('task_queue_crm_msg_history', json.dumps(user_task))
            
            current_time = datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
            
            redis_client.lpush('task_queue_crm_msg_history',
                        json.dumps({"id_empresa":id_empresa,
                                    "message":f"IA: {llm_response}",
                                    "cliente": user_context['aluno']['codigo'],
                                    "tipoContato": "WA",
                                    "fase": fase_atual,
                                    "data": current_time}))
        else:
            logger.info(" [*] [messages_received_worker] No aluno code found")
    else:
        logger.info(" [*] [messages_received_worker] No aluno found")
    
    bq_.save_connected_phone(connected_phone)

    unset_pendency_verification(telefone, id_empresa)
    if not data.get('pendency_processed', False):
        set_pendency_verification(telefone, id_empresa, task, bq_)

    return log_response({
        "data": {
            "status": "success",
            "phone": telefone_envio,
            "response": llm_response,
            "id_empresa": id_empresa,
            "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
        },
        "table" : "mensagens_enviadas"
    })

def router_received_message(task):
    logger.info(f" [*] [router_received_message] Decidindo o handler")
    id_empresa, _ = get_from_instance(task.get("data", {}).get("instanceId", None))
    # TODO: Rever isso aqui
    is_rede = _is_rede(task.get("data", {}).get("instanceId", None))
    bq_ = bq(id_empresa=id_empresa)
    gym_data = bq_.get_gym_context()
    is_rede = gym_data.get("is_rede", is_rede)
    if is_rede:
        logger.info(f" [*] [router_received_message] encaminhado para o handle rede")
        task["id_matriz"] = id_empresa
        response = handle_received_message_rede(task)

    else:
        logger.info(f" [*] [router_received_message] encaminhado para o handle normal")
        task["id_empresa"] = task["id_empresa"] if task["id_empresa"] else id_empresa
        response = handle_received_message(task)
    
    return response

def run(redis_client, max_iter=None):
    
    logger.info(" [*] [messages_received_worker] Waiting for tasks")
    iter_count = 0
    while True:
        if max_iter and iter_count >= max_iter:
            break
        queue_size = redis_client.llen('messages_received_routerllm')
        logger.info(f" [x] [messages_received_worker] Task queue size: {queue_size}")
        task = redis_client.brpop('messages_received_routerllm')
        if task:
            logger.info(f"\n\n\n{'Nova Task':-^100}\n\n\n")
            task = json.loads(task[1].decode('utf-8'))
            try:
                # Pega o texto, telefone e id_empresa da task
                text = task.get('data', {}).get('text', {}).get('message', None)
                phone = parse_phone(task.get('data', {}).get('phone', None))
                instance_id = task.get('data', {}).get('instanceId')
                id_empresa, _ = get_from_instance(instance_id)

                if text is None or phone is None or id_empresa is None:
                    logger.error(" [*] [messages_received_worker] Error: text, phone or id_empresa is None")
                    response = router_received_message(task,)
                    iter_count += 1
                    continue
                # add response to the sent messages list
                # Adiciona a mensagem à lista de mensagens recentes
                connections.redis_client.append(
                    f"recent_messages:{id_empresa}:{phone}",
                    f"{text}\n"
                )

                handle_received_message(task)

                # if not connections.redis_client.exists(
                #     f"messages_jobs:{id_empresa}:{phone}:job_id"
                #     ):
                #     # Cria um job para lidar com a mensagem depois de X segundos

                #     # Tá dando problema no routter esse atraso proposital nas mensagens
                #     job = connections.queue.enqueue_in(
                #         time_delta=timedelta(seconds=SECONDS_TO_WAIT_TILL_RESPONSE),
                #         func=handle_received_message,
                #         args=(task,))
                #     # Adiciona o id do job à lista de jobs
                #     # para que não seja criado outro job para o mesmo telefone

                    
                #     connections.redis_client.set(
                #         f"messages_jobs:{id_empresa}:{phone}:job_id",
                #         "teste123", #job.id,
                #         ex=5*60
                #         )


            except Exception as e:
                import traceback
                logger.error(f"Error: {e}")
                traceback_ = traceback.format_exc()
                logger.error(traceback_)
                data = task.get('data', {})
                try:
                    instance_id = data.get('instanceId')
                    id_empresa, _ = get_from_instance(instance_id)
                except:
                    id_empresa = None
                response = {
                    "data": {
                        "status": "error",
                        "phone": data.get('phone', data.get('telefone', None)),
                        "response": str(e),
                        "id_empresa": id_empresa,
                        "data_envio": datetime.now().strftime('%Y-%m-%dT%H:%M:%S'),
                        "traceback": traceback_
                    },
                    "table" : "mensagens_enviadas"
                }
                redis_client.lpush('logs', json.dumps(response))
                traceback.print_exc()
                logger.error(f" [x] [messages_received_routerllm] Error: {e}")

        iter_count += 1
