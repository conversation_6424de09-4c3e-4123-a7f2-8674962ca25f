import time
import json
from functools import wraps
from threading import Thread
import requests.exceptions
import requests
import logging
from opentelemetry import trace
from opentelemetry.semconv.trace import SpanAttributes
from pandas import Timestamp, DataFrame
import os
import traceback
import re
from datetime import datetime
from src.connections.connections import Connections
from src.connections.jaeger import JaegerConnection
import uuid

ENV_MODE = os.getenv("MODE", "default")
PROD_ENV = os.getenv("FLASK_ENV", "development") == "production"
DEBUG_MODE_TRACE_METHODS = os.getenv("DEBUG_TRACE_METHODS", "False").lower() in ("true", "1", "t")

connections = Connections.get_instance()
logger = logging.getLogger("conversas_logger")

if ENV_MODE == "default":
    logger.warning("A variável de ambiente MODE não foi definida. Isso vai impactar a aplicação.")

if ENV_MODE == 'api':
    from flask import request


def generate_whatsapp_link(telefone: str, origin="z_api"):
    """
    Retorna um link de contato para um número específico
    """
    telefone = parse_phone(telefone, origin)
    return f"https://wa.me/{telefone.replace('+', '')}"


def count_links(text: str) -> int:
    """
    Conta o número de links (URLs) em uma string de texto.

    Esta função usa uma expressão regular para encontrar URLs que começam
    com 'http://', 'https://' ou 'www.'.

    Args:
      text: A string de texto que será analisada.

    Returns:
      O número inteiro de links encontrados no texto.
    """
    # Expressão regular para encontrar URLs.
    # r'' indica uma "raw string", que trata barras invertidas como caracteres literais.
    # (?:https?://|www\.) - Procura por 'http://', 'https://' ou 'www.'.
    # [^\s/$.?#].[^\s]* - Procura por uma sequência de caracteres que não sejam espaços em branco.
    url_pattern = re.compile(r'(?:https?://|www\.)[^\s/$.?#].[^\s]*')

    found_links = url_pattern.findall(text)

    return len(found_links)


def get_id_conversa(
    telefone: str, id_empresa: str, return_info: bool =  False
) -> str | tuple[str, dict]:
    """
    Retorna o ID da conversa para o telefone e empresa fornecidos.
    """
    info = {
        "new": False
    }
    id_conversa = connections.redis_client.get(
        f"current_conversation:{telefone}-{id_empresa}"
    )
    if id_conversa is None:
        info["new"] = True
        logger.info(
            f"Nenhum ID de conversa encontrado para o telefone {telefone} e empresa {id_empresa}."
        )
        id_conversa = str(uuid.uuid4()).encode('utf-8')
        connections.redis_client.set(
            f"current_conversation:{telefone}-{id_empresa}",
            id_conversa
        )
    if return_info:
        return id_conversa.decode('utf-8'), info

    return id_conversa.decode('utf-8')

def register_indicator(
    identificador: str, id_empresa: str, indicador: str = "",
    telefone: str = "", nome: str = "", meta: dict = {}
) -> None:
    """
    Manda os dados de indicadores para a fila de indicadores.
    """
    indicador = {
        "id_conversa": get_id_conversa(telefone, id_empresa),
        "id_empresa": id_empresa,
        "telefone": telefone, 
        "data_hora_evento": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "identificador": identificador,
        "indicador": indicador,
        "nome": nome,
        "meta": meta
    }
    connections.redis_client.lpush('indicators_queue', json.dumps({
        "data": indicador
    }))

def log_func_time(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.monotonic_ns() / 1_000_000
        result = func(*args, **kwargs)
        end_time = time.monotonic_ns() / 1_000_000
        raw_elapsed = round(end_time - start_time)
        elapsed = format_seconds_to_readable(raw_elapsed / 1_000)
        logger.info(f" [FUNC_TIME]-{func.__name__}-{raw_elapsed}ms")
        logger.info(f"Função {func.__name__} demorou {elapsed} para rodar.")
        return result
    return wrapper


class Colors:
    def __init__(self):
        self.colors = {
            "green": "\033[92m",
            "yellow": "\033[93m",
            "red": "\033[91m",
            "end": "\033[0m"
        }

    def colorize_text(self, text, color):
        return f"{self.colors[color]}{text}{self.colors['end']}"


def run_in_thread(fn):
    @wraps(fn)
    def wrapper(*args, **kwargs):
        thread = Thread(target=fn, args=args, kwargs=kwargs, daemon=True)
        thread.start()
        return thread
    return wrapper


def format_seconds_to_readable(seconds: float) -> str:
    """
    Formata um número de segundos em uma string legível.
    """
    if seconds < 0:
        raise ValueError("O número de segunds não pode ser negativo.")

    hours, remainder = divmod(seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    miliseconds, microseconds = divmod(seconds * 1000, 1)
    nanoseconds = int((seconds * 1_000_000_000) % 1000)


    result = ""
    if hours > 0:
        result += f"{int(hours)}h, "
    if minutes > 0:
        result += f"{int(minutes)}min, "
    if seconds > 0:
        result += f"{int(seconds)}s,"
    if miliseconds > 0:
        result += f"{int(miliseconds)}ms,"
    if nanoseconds > 0:
        result += f"{float(nanoseconds)}ns"

    return result.strip(", ")


class DynamicModuleFilter(logging.Filter):
    """Filtro otimizado para adicionar o nome do módulo chamador e telefone aos logs."""

    def __init__(self, phone: str, task_id: str):
        super().__init__()
        self.phone = phone
        self.task_id = task_id

    def filter(self, record):
        # Extrai o nome do módulo diretamente do caminho do arquivo
        record.module_name = os.path.basename(record.pathname).replace('.py', '')

        # Adiciona o phone ao registro
        record.phone = self.phone

        record.task_id = self.task_id

        return True
    
def trace_method(debug: bool = False):
    """
    Fábrica de decoradores que loga a origem de um método quando ele é chamado.
    Só loga se o 'debug' for True ou se a variável de ambiente DEBUG_TRACE_METHODS for True.

    Uso:
    @trace_method(debug=True)
    def my_function():
        ...
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if debug or DEBUG_MODE_TRACE_METHODS:
                class_name = ""
                # se for um método de classe, o primeiro argumento é 'self'
                if args and hasattr(args[0], '__class__'):
                    class_name = f"{args[0].__class__.__name__}."
                
                module_name = func.__module__
                
                logger.info(f"[CALLED_METHOD] -> {module_name}.{class_name}{func.__name__}")
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                # O log de erro é sempre importante, então o mantemos fora da condição de debug.
                # Se for um método, pegue o nome da classe
                class_name = ""
                if args and hasattr(args[0], '__class__'):
                    class_name = f"{args[0].__class__.__name__}."
                module_name = func.__module__
                
                logger.error(f"[METHOD_ERROR] -> {module_name}.{class_name}{func.__name__}: {e}", exc_info=True)
                raise Exception from e
        return wrapper
    return decorator


class RoutesTracing:
    """
    Decorador para adicionar rastreamento a rotas Flask usando OpenTelemetry e Jaeger.

    Este decorador inicia automaticamente um intervalo para cada função decorada, 
    captura detalhes da solicitação (método, URL, agente do usuário, IP do cliente) e 
    define como atributos do intervalo. 
    
    Ele também permite capturar campos específicos do corpo da solicitação e parâmetros da consulta. 
    O decorador manipula exceções, definindo o status do intervalo como ERRO e registrando os detalhes da exceção.

    O decorador assume que a função decorada é uma rota Flask e tem acesso ao objeto `request` do Flask.

    Paramêtros:
    * `span_name_prefix: str = None`: prefixo para o nome do span
    * `span_description: str = None`: descrição do span
    * `capture_body_fields: list = None`: lista de campos a serem capturados do corpo da solicitação
    * `capture_query_params: list = None`: lista de parâmetros a serem capturados da consulta

    Exemplo de uso:
    ```python
    @RoutesTracing(
        span_name_prefix="nome_do_span",
        span_description="descrição_do_span",
        capture_body_fields=["campo1", "campo2"],
        capture_query_params=["parametro1", "parametro2"]
    )
    def minha_rota():
        ...
    ```
    """
    def __init__(self,
                 span_name_prefix: str = None,
                 span_description: str = None,
                 capture_body_fields: list = None,
                 capture_query_params: list = None):
        self.span_name_prefix = span_name_prefix
        self.span_description = span_description or "No description provided"
        self.capture_body_fields = capture_body_fields or []
        self.capture_query_params = capture_query_params or []

    def __call__(self, func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                jaeger = JaegerConnection().connect(service_name="orion-api")
                tracer = JaegerConnection().get_tracer()
            except RuntimeError as runtime_error:
                logger.warning(f"Erro ao obter o tracer: {runtime_error}")
                return func(*args, **kwargs)
            
            span_name = f"{self.span_name_prefix}.{func.__name__}" \
                        if self.span_name_prefix else func.__name__

            with tracer.start_as_current_span(span_name) as span:
                try: # span.set_attribute... abaixo são os dados padrão para todas as rotas
                    span.set_attribute(SpanAttributes.HTTP_METHOD, request.method)
                    span.set_attribute(SpanAttributes.HTTP_URL, request.url)
                    span.set_attribute(SpanAttributes.HTTP_USER_AGENT, request.user_agent.string)
                    span.set_attribute(SpanAttributes.HTTP_CLIENT_IP, request.remote_addr)

                    if self.span_description: # descrição é opcional mas parece ser boa prática da comunidade
                        span.set_attribute("operation.description", self.span_description)

                    if self.capture_body_fields and request.is_json: # captura os dados de entrada do endpoint
                        try:
                            req_data = request.json
                            for field in self.capture_body_fields:
                                if field in req_data:
                                    span.set_attribute(f"request.body.{field}", str(req_data[field]))
                        except Exception as e:
                            span.add_event("failed_to_capture_body_fields", {"error": str(e)})

                    if self.capture_query_params: # se tiver query sql captura os dados
                        for param_key in self.capture_query_params:
                            if param_key in request.args:
                                span.set_attribute(
                                    f"request.query.{param_key}",
                                    request.args.get(param_key)
                                )
                    
                    result = func(*args, **kwargs)
                    status_code = 200 # assume que é 200 e verifica se é diferente

                    if isinstance(result, tuple) and len(result) >= 2 and isinstance(result[1], int):
                        status_code = result[1]
                    else: # assume que é 200 se não tiver status_code, mas deve ser melhorado posteriormente
                        status_code = getattr(result, 'status_code', 200)

                    span.set_attribute(SpanAttributes.HTTP_STATUS_CODE, status_code)

                    span.add_event("operation.completed_successfully")
                    
                    return result
                except Exception as tracer_error:
                    span.set_status(trace.StatusCode.ERROR, f"Operação falhou: {tracer_error}")
                    span.add_event(
                        "exception",
                        {
                            SpanAttributes.EXCEPTION_TYPE: type(tracer_error).__name__,
                            SpanAttributes.EXCEPTION_MESSAGE: str(tracer_error),
                            SpanAttributes.EXCEPTION_STACKTRACE: traceback.format_exc(),
                        }
                    )
                    raise

        return wrapper


class WorkersTracer:
    """
    Decorador para adicionar rastreamento a funções de worker usando OpenTelemetry e Jaeger.

    Este decorador inicia automaticamente um intervalo para cada função decorada, 
    captura atributos específicos fornecidos e registra eventos de conclusão ou exceções.
    
    O decorador é projetado para ser usado em funções que executam tarefas em qualquer 
    contexto do projeto, exceto em rotas Flask que tem seu próprio decorador.

    Parâmetros:
    * `span_name_prefix: str = None`: Prefixo para o nome do intervalo.
    * `span_description: str = None`: Descrição do intervalo.
    * `span_attributes: dict = None`: Dicionário de atributos a serem adicionados ao intervalo.

    Exemplo de uso:
    ```python
    @WorkersTracer(
        span_name_prefix="nome_do_span",
        span_description="descrição_do_span",
        span_attributes={"chave1": "valor1", "chave2": "valor2"}
    )
    def minha_funcao():
        ...
    """
    def __init__(
            self,
            span_name_prefix: str = None,
            span_description: str = None,
            span_attributes: dict = None,
        ):
        self.span_name_prefix = span_name_prefix
        self.span_description = span_description or "No description provided"
        self.span_attributes = span_attributes or {}

    def __call__(self, func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                jaeger = JaegerConnection().connect(service_name="orion-worker")
                tracer = JaegerConnection().get_tracer()
            except RuntimeError as runtime_error:
                logger.warning(f"Erro ao obter o tracer: {runtime_error}")
                return func(*args, **kwargs)
            
            span_name = f"{self.span_name_prefix}.{func.__name__}" \
                        if self.span_name_prefix else func.__name__

            with tracer.start_as_current_span(span_name) as span:
                try:
                    span.set_attribute("operation.description", self.span_description) # descrição do span
                    if isinstance(self.span_attributes, dict):
                        for key, value in self.span_attributes.items(): # itera sobre os valores do dicionário definido no decorador
                            span.set_attribute(key, value)
                    elif callable(self.span_attributes):
                        try:
                            dynamic_attributes = self.span_attributes(*args, **kwargs)
                            if isinstance(dynamic_attributes, dict):
                                for key, value in dynamic_attributes.items():
                                    span.set_attribute(key, value)
                            else:
                                logger.warning(
                                    f"Atributo 'callable' em span_attributes para {func.__name__} não é um dicionário válido."
                                )
                        except Exception as callable_executor_error:
                            logger.warning(
                                f"Erro ao executar callable em span_attributes para '{func.__name__}': {callable_executor_error}"
                            )

                    
                    result = func(*args, **kwargs)
                    span.add_event("operation.completed_successfully")

                    return result
                except Exception as tracer_error:
                    span.set_status(trace.StatusCode.ERROR, f"Operação falhou: {tracer_error}")
                    span.add_event(
                        "exception",
                        {
                            SpanAttributes.EXCEPTION_TYPE: type(tracer_error).__name__,
                            SpanAttributes.EXCEPTION_MESSAGE: str(tracer_error),
                            SpanAttributes.EXCEPTION_STACKTRACE: traceback.format_exc(),
                        }
                    )
                    raise

        return wrapper


class DatabaseOpsTracer:
    """
    Decorador para adicionar rastreamento às operações de banco de dados usando OpenTelemetry e Jaeger.

    Este decorador inicia automaticamente um intervalo para cada função decorada, 
    captura atributos específicos fornecidos e registra eventos de conclusão ou exceções.
    
    O decorador é projetado para ser usado em funções que executam tarefas em operações de consultas 
    no banco de dados deste projeto, exceto em rotas Flask e Workers que tem seus próprios decoradores.

    Parâmetros:
    * `span_name_prefix: str = None`: Prefixo para o nome do intervalo.
    * `span_description: str = None`: Descrição do intervalo.
    * `span_attributes: dict = None`: Dicionário de atributos a serem adicionados ao intervalo.

    Exemplo de uso:
    ```python
    @DatabaseOpsTracer(
        span_name_prefix="nome_do_span",
        span_description="descrição_do_span",
        span_attributes={"chave1": "valor1", "chave2": "valor2"}
    )
    def minha_funcao():
        ...
    """
    def __init__(
            self,
            span_name_prefix: str = None, # nome do span (prefixo)
            span_description: str = None, # descrição do span (tipo de consulta que está sendo executada)
            span_attributes: dict = None, # alguns atributos para a query
        ):
        self.span_name_prefix = span_name_prefix
        self.span_description = span_description or "No description provided"
        self.span_attributes = span_attributes or {}

    def __call__(self, func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                jaeger = JaegerConnection().connect(service_name="orion-database-psql")
                tracer = JaegerConnection().get_tracer()
            except RuntimeError as runtime_error:
                logger.warning(f"Erro ao obter o tracer: {runtime_error}")
                return func(*args, **kwargs)
            
            span_name = f"{self.span_name_prefix}.{func.__name__}" \
                        if self.span_name_prefix else func.__name__

            with tracer.start_as_current_span(span_name) as span:
                try:
                    span.set_attribute("operation.description", self.span_description) # descrição do span
                    if isinstance(self.span_attributes, dict):
                        for key, value in self.span_attributes.items(): # itera sobre os valores do dicionário definido no decorador
                            span.set_attribute(key, value)
                    elif callable(self.span_attributes):
                        try:
                            dynamic_attributes = self.span_attributes(*args, **kwargs)
                            if isinstance(dynamic_attributes, dict):
                                for key, value in dynamic_attributes.items():
                                    span.set_attribute(key, value)
                            else:
                                logger.warning(
                                    f"Atributo 'callable' em span_attributes para {func.__name__} não é um dicionário válido."
                                )
                        except Exception as callable_executor_error:
                            logger.warning(
                                f"Erro ao executar callable em span_attributes para '{func.__name__}': {callable_executor_error}"
                            )

                    
                    result = func(*args, **kwargs)
                    span.add_event("operation.completed_successfully")

                    return result
                except Exception as tracer_error:
                    span.set_status(trace.StatusCode.ERROR, f"Operação falhou: {tracer_error}")
                    span.add_event(
                        "exception",
                        {
                            SpanAttributes.EXCEPTION_TYPE: type(tracer_error).__name__,
                            SpanAttributes.EXCEPTION_MESSAGE: str(tracer_error),
                            SpanAttributes.EXCEPTION_STACKTRACE: traceback.format_exc(),
                        }
                    )
                    raise

        return wrapper


def add_custom_handler(logger: logging.Logger, phone: str = '', task_id: str = ''):
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    handler = logging.StreamHandler()
    formatter = logging.Formatter(
        '%(levelname)s - %(phone)s - %(task_id)s - %(asctime)s - %(module_name)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)

    for filter in logger.filters[:]:
        logger.removeFilter(filter)

    # Adiciona o filtro personalizado
    logger.addFilter(DynamicModuleFilter(phone, task_id))

    logger.setLevel(logging.INFO)
    logger.propagate = False


def log_func_name(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        logger.info(f"\n\nINICIO - {func.__name__:-^100}\n\n")
        result = func(*args, **kwargs)
        logger.info(f"\n\nFIM - {func.__name__:-^100}\n\n")
        return result
    return wrapper

def task_sender(queue_name, msg, **kwargs):
    logger.info(f"\n\n\nEnviando tarefa para a fila {queue_name} com a mensagem: {msg}\n\n\n")
    try:
        indicador = kwargs.get("indicador")
        if indicador:
            register_indicator(
                identificador="proativo",
                id_empresa=kwargs.get("id_empresa"),
                indicador=indicador,
                telefone=parse_phone(
                    kwargs["phone"],
                    kwargs["origin"]
                ),
                nome=kwargs.get("nome", "")
            )
        task = {
            "id_empresa": kwargs["id_empresa"],
            "data": {
                "instanceId": kwargs["instanceId"],
                "connectedPhone": kwargs["connectedPhone"],
                "phone": kwargs["phone"],
                "isGroup": kwargs["isGroup"],
                "participantPhone": None,
                "messageId": "2c784912-ad74-4472-9e13-7bd7e9461b80",
                "chave_empresa": None,
                "momment": datetime.timestamp(datetime.now())*1000,
                "sessionId": kwargs["sessionId"],
                "departamento": "ConversasAI",
                "colaborador": None,
                "text": {
                    "message": msg
                },
                "requiresValidation": kwargs.get("requiresValidation", False),
                "validationType": kwargs.get("validationType", None),
                "validateValue": kwargs.get("validateValue", None),
            },
            "sessionId": None,
            "origin": kwargs["origin"],
            "canAnswer": True
        }
    except KeyError as e:
        logger.warning("A Task foi mal configurada! Alguns valores são obrigatórios: %s", e)
        return

    import redis
    redis_conn = redis.from_url(os.getenv("REDIS_URL", "redis://localhost:6379/0"))
    redis_conn.rpush(queue_name, json.dumps(task))

def say_hello():
    logger.info("Hello, world!")

def validar_token(token):
    regex = r"^pn_[A-Za-z0-9]"
    return bool(re.match(regex, token))

def validar_id_empresa(id_empresa):
    regex = r"^[a-zA-Z0-9]{25,}-[0-9]+$"
    return bool(re.match(regex, id_empresa))


def token_guard(token):
    return f"****{token[-4:]}"

def split_date(date):
    if '.' in str(date):
        return str(date).split('.')[0]
    elif '+' in str(date):
        return str(date).split('+')[0]

def parse_phone(telefone: str, origin: str = "z_api") -> str:
    """
    Format phone number to international format.
    """

    if telefone is None:
        return None

    telefone = telefone.replace("|", "")

    if origin == "gym_bot":
        
        telefone = (telefone
            .replace("(", "")
            .replace(")", "")
            .replace("-", "")
            .replace(" ", "")
        )
        if "55" not in telefone:
            telefone = "55" + telefone

    if "-" in telefone:
        telefone = telefone.split("-")[0]

    if not "+" in telefone:
        telefone = "+"+telefone

    # Extracting information from the received data
    if origin=="z_api" and len(telefone) == 13:
        prefix = telefone[:5]
        suffix = telefone[5:]
        telefone = f"{prefix}9{suffix}"

    return telefone

def retry(retries=3, delay=2, backoff=2, status_codes=(500,), exceptions=(requests.exceptions.RequestException, TypeError)):
    """
    Decorator for retrying a function call with exponential backoff based on status codes or exceptions.
    
    :param retries: Number of attempts before failing
    :param delay: Initial delay between retries
    :param backoff: Multiplier for delay between each retry
    :param status_codes: HTTP status codes that trigger a retry
    :param exceptions: Exceptions to catch for retrying
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            attempt = 0
            current_delay = delay
            while attempt < retries:
                try:
                    response = func(*args, **kwargs)
                    if isinstance(response, dict) and response.get('status_code') in status_codes:
                        raise requests.exceptions.RequestException(f"HTTP {response.get('status_code')}")
                    return response
                except exceptions as e:
                    last_exception = e
                    logger.exception(f"Erro na requisição: {e} na função {func.__name__}. Tentando novamente em {current_delay} segundos...")
                    traceback_ = traceback.format_exc()
                    time.sleep(current_delay)
                    attempt += 1
                    current_delay *= backoff
            logger.exception(f"Falha na requisição após {retries} tentativas.")
            task = {
                    "data" : {
                    "data_registro": Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "traceback": traceback_,
                    "ambiente": os.getenv("FLASK_ENV", "development")
                    },
                    "table": "erros_terminais"
                }
            connections.redis_client.rpush('logs', json.dumps(task))
            raise last_exception  # Relança explicitamente a última exceção
        return wrapper
    return decorator

def generate_curl_command(url, req_body, headers, method="POST", params=None):
    json_data = json.dumps(req_body)

    if params:
        url = requests.Request('GET', url, params=params).prepare().url

    curl_command = f"curl -X {method} '{url}'"

    for key, value in headers.items():
        curl_command += f" -H '{key}: {value}'"

    curl_command += f" -d '{json_data}'"

    return curl_command

def register_log(
    url, req_body, headers,
    method, response: requests.Response, function,
    id_empresa, params=None, table="requests_sistema_pacto",
    send_logs_to=None,
) -> None:
    curl_command = generate_curl_command(url, req_body, headers, method, params)

    logger.info(f"\n\n[REGISTER_LOG] Registrando log para a função {function} com o comando cURL: {curl_command}")
    try:
        status_code = response.status_code
    except:
        status_code = 0
    logger.info(f"[REGISTER_LOG] Status code da resposta: {status_code}")
    try:
        response = json.dumps(response.json())
    except:
        try:
            response = response.text
        except:
            response = response
    logger.info(f"[REGISTER_LOG] Resposta da requisição: {str(response)[:200]}\n\n")
    task = {
        "data": {
            "data_requisicao": Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'),
            "id_empresa": id_empresa,
            "function": function,
            "request": curl_command,
            "response": response,
            "status_code": status_code
        },
        "table": table
    }
    if isinstance(send_logs_to, dict) and send_logs_to.get("webhook_url"):
        test_name = send_logs_to.get("test_name")
        url = send_logs_to.get("webhook_url")
        response = requests.post(
            url=f"{url}/log?test_name={test_name}",
            json=task,
            headers={
                "Content-Type": "application/json",
            }
        )
        connections.redis_client.rpush(f'static_logs:{test_name}', json.dumps(task))
    connections.redis_client.rpush('logs', json.dumps(task))

def timestamp_formatado(weekday=False):
    dias_da_semana = {
        'Monday': 'Segunda-feira',
        'Tuesday': 'Terça-feira',
        'Wednesday': 'Quarta-feira',
        'Thursday': 'Quinta-feira',
        'Friday': 'Sexta-feira',
        'Saturday': 'Sábado',
        'Sunday': 'Domingo'
    }

    now = Timestamp.now()

    if weekday:
        dia_da_semana = dias_da_semana[now.strftime('%A')]
        formatted_timestamp = now.strftime(f'{dia_da_semana}, %d/%m/%Y %H:%M:%S')
    else:
        formatted_timestamp = now.strftime('%d/%m/%Y %H:%M:%S')

    return formatted_timestamp

def monitor_health(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        redis_client = connections.redis_client
        base_key = f"health_check:{func.__module__}"
        try:
            redis_client.set(f"{base_key}", json.dumps({"last_run":Timestamp.now().isoformat()}))
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Erro na função {func.__module__}: {e}")
            data = json.loads(redis_client.get(f"{base_key}"))
            data["last_error"] = str(e)
            data["last_error_timestamp"] = Timestamp.now().isoformat()
            data["last_error_traceback"] = traceback.format_exc()
            data["errors"] = data.get("errors", 0) + 1
            redis_client.set(f"{base_key}", json.dumps(data))
            redis_client.expire(f"{base_key}", 60*60*24)
            raise
    return wrapper

def is_running(key):
    redis_client = connections.redis_client
    
    health_check = redis_client.get(f"health_check:{key}")
    logger.info(f"Health check: {health_check}")
    if health_check is None:
        return False
    data = json.loads(health_check)
    logger.info(f"Data: {data}")
    if "last_error" in data.keys():
        return False
    return True

    
    
def get_integrations():
    return ["pacto", "firebase"] # precisa ser alterado quando tiverem as integrações
    integrations = []
    for integration in os.listdir('./src/integrations'):
        integrations.append(integration)

    return integrations

def get_sensitive_data_by_integragtion(integration: str = "pacto"):
    functions = {
        "pacto":   
        {
            "dont_respond": "ignore",
            "end_conversation": "ignore",
            "get_additional_context": "regenerate",
            "warn_user": "regenerate",
            "save_user_level": "regenerate",
            "save_user_birthdate": "regenerate",
            "check_classes_day": "regenerate",
            "check_class_details": "regenerate",
            "book_class": "regenerate",
            "book_call": "regenerate",
            "save_user_name": "regenerate",
            "register_visitor": "regenerate",
            "search_by_cpf": "regenerate",
            "generate_train": "regenerate"
        }
    }

    return functions.get(integration, {})

def get_uuid(val, version=4):
    try:
        return str(uuid.UUID(str(val), version=version))
    except ValueError:
        logger.info(f"[GET_UUID] O UUID: {val} informado não é válido.")
        return None

def get_date(date_str, format="%Y-%m-%d %H:%M:%S") -> datetime:
    try:
        return datetime.strptime(str(date_str), format)
    except ValueError:
        logger.info(f"[GET_DATE] A data: {date_str} informada não é válida.")
        return None

def safe_docs(f):
    """
    Decorator factory to exclude routes from documentation if environment is production.
    """
    if PROD_ENV:
        # Do not apply @wraps, effectively hiding metadata
        def wrapper(*args, **kwargs):
            return f(*args, **kwargs)
    else:
        # Apply @wraps to preserve metadata
        @wraps(f)
        def wrapper(*args, **kwargs):
            return f(*args, **kwargs)
    return wrapper

def get_device_from_z_api(instance_id, token):
    """
    Função para buscar informações do dispositivo na Z API.
    """
    client_token = os.getenv('Z_API_CLIENT_TOKEN')
    url = f'https://api.z-api.io/instances/{instance_id}/token/{token}/device'
    response = requests.get(
        url=url,
        headers={'client-token': client_token},
        timeout=10
    )

    if response.status_code == 200:
        if (phone := response.json().get('phone')):
            return phone
    return None

def validate_cpf(cpf: str) -> bool:
    """
    Função para validar CPF.
    """
    cpf = cpf.replace(".", "").replace("-", "")
    if len(cpf) != 11:
        return False
    if cpf in [s * 11 for s in [str(n) for n in range(10)]]:
        return False
    soma = 0
    for i in range(9):
        soma += int(cpf[i]) * (10 - i)
    resto = 11 - (soma % 11)
    if resto == 10 or resto == 11:
        resto = 0
    if resto != int(cpf[9]):
        return False
    soma = 0
    for i in range(10):
        soma += int(cpf[i]) * (11 - i)
    resto = 11 - (soma % 11)
    if resto == 10 or resto == 11:
        resto = 0
    if resto != int(cpf[10]):
        return False
    return True

if ENV_MODE in ("worker", "router"):
    from enum import Enum
    from langchain_core.utils.function_calling import convert_to_openai_function
    from src.worker.llm_modules.llm_utils.openai_util_module import OpenAIUtilModule
    from src.worker.llm_modules.llm_utils.models import (
        VoiceScheduleType,
        VoiceSchedule,
        Schedule,
        VoiceScheduleTypeExamples,
        ChatPendency,
        ConversationSuccess,
        ConclusaoMetaDiariaType,
        ClassificacaoMetaDiariaType,
        AcoesMetaDiaria,
        Objecao,
        Agendamento,
        NotificationSchedule,
        SimplesRegistro,
        PhaseScheduleStrategy
    )

    def convert_to_together_function(func: callable) -> dict:
        try:
            function = convert_to_openai_function(func)
            return {"type": "function", "function": function}
        except Exception as e:
            logger.exception(f"Erro na função {func.__name__}: {e}")
            return None

    @retry(retries=3, delay=2, backoff=2, status_codes=(500, 502, 503, 504), exceptions=(requests.exceptions.RequestException, TypeError, AttributeError))
    def identify_voice(personality: str, bq) -> VoiceSchedule:
        """
        Utiliza LLM para identificar a voz apropriada com base na personalidade e no contexto.
        """
        base_prompt = f"Essa é a personalidade: {personality}"

        llm = OpenAIUtilModule(bq)

        system_prompt = ("Você é um assistente inteligente, capaz de identificar qual o tipo de agendamento de voz apropriado com base na personalidade fornecida."
                        "Se for necessário, você pode definir um agendamento de voz para personalizar a voz com base no horário."
                        f"Os tipos de agenda de voz disponíveis são: {VoiceScheduleType._member_names_}"
                        "Hourly deve ser usado para definir um agendamento de voz por hora, Weekly para definir um agendamento de voz por dia da semana e" 
                        "None para definir uma voz fixa, caso a personalidade não se altere.")
        
        response = llm.get_response(system_prompt, base_prompt, Schedule, dump=False)

        schedule_type = response.type.value

        schedule_model = getattr(VoiceSchedule, schedule_type)

        schedule_example = getattr(VoiceScheduleTypeExamples, schedule_type)

        system_prompt = ("Você é um assistente inteligente, capaz de identificar a voz apropriada com base na personalidade fornecida."
                        "Se for necessário, você pode definir um agendamento de voz para personalizar a voz com base no horário."
                        "Você leva em consideração o gênero da personalidade para saber qual voz usar."                        )
        
        prompt = base_prompt + ("Gere um arquivo de voz com base nessas informações."
                                f"Exemplo GENÉRICO de agendamento de voz: {json.dumps(schedule_example)}")

        llm_response = llm.get_response(system_prompt, prompt, schedule_model)

        data = {
            "schedule_type": schedule_type,
            "data": llm_response.get("data")
        }

        return data

    def determine_voice(id_empresa) -> str:
        """
        Seleciona a voz apropriada com base no contexto e no horário.

        :param id_empresa: ID da empresa.
        """
        try:
            schedule = json.loads(connections.redis_client.get(f"voice_schedule:{id_empresa}"))
            voices = { "feminino": "nova", "masculino": "onyx" }

            if schedule:
                current_hour = Timestamp.now().hour
                current_weekday = Timestamp.now().strftime('%A').capitalize()
                print(f"current_hour: {current_hour}")
                print(f"current_weekday: {current_weekday}")
                schedule_type = schedule.get("schedule_type")
                data = schedule.get("data")
                if schedule_type == "hourly":
                    data = {item.get("hour"): item.get("voice") for item in data}
                    closest_hour = min(data.keys(), key=lambda x: abs(int(x.split(":")[0]) - current_hour))
                    selected_voice = voices.get(data.get(closest_hour), "nova")

                elif schedule_type == "weekly":
                    data = {item.get("day"): item.get("voice") for item in data}
                    selected_voice = voices.get(data.get(current_weekday), "nova")

                else:
                    selected_voice = voices.get(data, "nova")
            else:
                selected_voice = "nova"

            return selected_voice

        except Exception as e:
            logger.exception(f"Erro ao determinar a voz: {e}")
            return "nova"

    @retry(retries=3, delay=2, backoff=2, status_codes=(500, 502, 503, 504), exceptions=(requests.exceptions.RequestException, TypeError, AttributeError))    
    def identify_pendency(id_empresa, telefone, bq) -> tuple[bool, str]:
        """
        Utiliza LLM para identificar pendências na conversa e retorna se há alguma pendência.
        """
        if not os.getenv("CHECK_PENDENCY", "false") == "true":
            return False, ""
        llm = OpenAIUtilModule(bq)
        messages = connections.redis_client.get(f"last_messages-{telefone}-{id_empresa}")
        if messages:
            messages = json.loads(messages)
            messages = messages[-4:]
            system_prompt = """
            Você é um assistente inteligente, capaz de identificar pendências na conversa por parte do assistente com base nas mensagens anteriores.
            Se o assistente tiver dito que não possui informações suficientes para responder a uma pergunta, isso não deve ser considerado uma pendência.
            Por exemplo, se o assistente disse que faria alguma ação, mas ela não foi realizada, isso pode ser considerado uma pendência.
            Caso o usuário tenha tentado resolver a pendência, mas tenha dado algum erro, isso não deve ser considerado uma pendência.
            Você deve considerar o contexto da conversa para identificar as pendências.
            Normalmente, se o usuário enviou dados que precisam ser processados, mas o assistente não executou a ação por meio de um function_call, isso pode ser considerado uma pendência.
            Você deve identificar se há alguma pendência na conversa, e, se houver, fazer uma descrição da pendência.
            **Apenas se preocupe com pendências extremamente graves por parte do assistente.**
            Se houver pendência do assistente, utilize "has_pendency", e se não houver pendência do assistente, utilize "no_pendency".
            """
            prompt = json.dumps(messages)
            response = llm.get_response(system_prompt, prompt, ChatPendency, dump=False)
            has_pendency = response.type.value == "has_pendency"
            pendency = response.pendency
            return has_pendency, pendency

    def classificao_meta_diaria(
            telefone: str,
            bq: any,
            pit: any,
            id_conversa: str
    ) -> tuple[str, str]:
        """
        Essa função é responsável por classificar a conversa em uma das categorias de meta diária.
        """
        mensagens: DataFrame = bq.get_messages(telefone, id_conversa)
        mensagens_list = []
        for mensagem in mensagens.iterrows():
            mensagens_list.append({
                "role": mensagem[1]["enviado_por"],
                "content": mensagem[1]["mensagem"]
            })

        if mensagens_list:

            llm = OpenAIUtilModule(bq)

            system_prompt = (
                "Você é faz parte da equipe de suporte técnico de uma empresa, seu trabalho é analisar as conversas com o cliente e classificar a sua categoria"
                f"As categorias possíveis são: {ConclusaoMetaDiariaType._member_names_}"
                "As instruções para classificar uma conversa em cada categoria são:"
                "agendamento: Agendar uma aula experimental, visita ou ligação"
                "objecao: Registrar o motivo/desculpa do cliente."
                "simples_registro: Fazer um comentário simples (ex. Tentou contato mas não respondeu)"
            )

            response = llm.get_response(system_prompt, json.dumps(mensagens_list, indent=2, ensure_ascii=False), ClassificacaoMetaDiariaType, dump=False)

            classification = response.type.value
            action_description = None
            action_model: Agendamento | Objecao | SimplesRegistro = getattr(AcoesMetaDiaria, classification)

            base_prompt = (
                f"Hoje é {timestamp_formatado(weekday=True)}\n"
                f"Você recebeu a seguinte conversa com um cliente:\n{json.dumps(mensagens_list, indent=2, ensure_ascii=False)}"
                f"Você classificou essa conversa como uma {classification}!"
            )
            if classification in ("agendamento", "objecao"):
                descriptions = pit.get_descriptions(classification)
                action_model.set_types(descriptions)
                action_type: Enum = action_model.__annotations__["tipo"]
                action_options = [getattr(action_type, obj).value for obj in dir(action_type) if not obj.startswith("__")]
                prompt = base_prompt + (
                    f"Agora, SUA tarefa é determinar qual o tipo de {classification} esse conversa se encaixa"
                    f"As opções são possíveis são:\n{action_options}"
                )
            else:
                prompt = base_prompt + (
                    f"Você deverá, agora, detalhar o que foi registrado nessa conversa."
                )

            response: Agendamento | Objecao | SimplesRegistro = llm.get_response(system_prompt, prompt, action_model, dump=False)
            if hasattr(response, "tipo"):
                action_description = response.tipo.value
            elif hasattr(response, "observacao"):
                action_description = response.observacao
            else:
                action_description = ""
            if isinstance(response, Agendamento):
                extra_info = {
                    "tipo": response.tipo_agendamento,
                    "horario": response.horario,
                    "data": response.data.strftime('%d/%m/%Y'),
                    "codigo_aula": response.codigo_aula
                }
                connections.redis_client.set(
                    f"registros_meta_diaria:{id_conversa}",
                    json.dumps(extra_info),
                    ex=8*60*60
                )

            return classification, action_description
        return None, None

    def check_for_sensitive_data(message: str, integration: str = "pacto") -> tuple[bool, str]:
        """
        Função para verificar se há dados sensíveis em uma mensagem.

        Args:
            message (str): Mensagem a ser verificada.
            integration (str): Integração para saber quais functions são sensíveis.
        """
        try:
            if not message:
                return False, ""
            actions = get_sensitive_data_by_integragtion(integration)
            sensitive_data = list(actions.keys())
            lower_message = message.lower()
            for data in sensitive_data:
                if data in lower_message:
                    return True, actions.get(data)
            return False, ""
        except Exception as e:
            logger.exception(f"Erro ao verificar dados sensíveis: {e}")
            return False, ""

    def analyze_conversation_success(
            telefone: str,
            objetivo: str,
            bq,
            id_conversa: str
            ) -> tuple[bool, str]:
        """
        Função para ler a conversa e identificar se foi bem sucedida.

        Args:
            messages (list[str]): Lista de mensagens da conversa.
            objective (str): Objetivo da conversa.
        """
        try:
            mensagens = bq.get_messages(telefone, id_conversa)
            mensagens_list = []
            for mensagem in mensagens.iterrows():
                mensagens_list.append({
                    "role": mensagem[1]["enviado_por"],
                    "content": mensagem[1]["mensagem"]
                })
            llm = OpenAIUtilModule(bq=bq)
            system_prompt = f"""
            Você é um assistente inteligente, capaz de identificar se uma conversa foi bem sucedida ou não com base nas mensagens trocadas.
            Você deve considerar o objetivo da conversa para identificar se ela foi bem sucedida.
            Se o objetivo da conversa foi alcançado, a conversa foi bem sucedida.
            Se o objetivo da conversa não foi alcançado, a conversa não foi bem sucedida.
            Você deve considerar o contexto da conversa para identificar se ela foi bem sucedida.
            Este era o objetivo da conversa: {objetivo}
            """
            prompt = json.dumps(mensagens_list)
            response = llm.get_response(system_prompt, prompt, ConversationSuccess, dump=False)
            success = response.type.value == "success"
            description = response.description
            logger.info("Análise de sucesso concluída.")
            return success, description, prompt
        except Exception as e:
            logger.exception(f"Erro ao analisar sucesso da conversa: {e}")
            return False, "", []
        
    def structure_notification_strategy(text: str, bq) -> dict:
        """
        Utiliza LLM para estruturar um texto não padronizado sobre estratégia de envio de mensagens
        de acordo com o modelo NotificationSchedule.

        Args:
            text (str): Texto não estruturado contendo a estratégia de notificação.
            bq: Instância do BigQueryData para passar ao LLM.

        Returns:
            dict: Dados estruturados da estratégia de notificação.
        """
        logger.info(f"Estruturando estratégia de notificação: {text}")
        try:
            llm = OpenAIUtilModule(bq)
            logger.info("Instância do LLM criada com sucesso.")
            
            system_prompt = """
                Você é um assistente especializado em estruturar informações sobre estratégias de envio de notificações.
                Sua tarefa é analisar um texto não estruturado sobre estratégias de envio de mensagens e convertê-lo em um formato padronizado.
                
                Existem três tipos principais de notificações:
                1. same_day: Notificações enviadas no mesmo dia depois do gendamento (ex: 1h, 2h depois do agendamento)
                2. event_day: Notificações enviadas no dia do evento antes ou depois (ex: 24h, 12h, 2h antes do evento ou após)
                3. intermediate_days: Notificações enviadas nos dias entre o agendamento e o evento (ex: 8h da manhã)
                
                Para cada tipo, você deve identificar os horários e mensagens correspondentes.
                Caso o texto não mencione um dos tipos, não inclua esse tipo na resposta.

                Não confunda, "same_day" é o dia que o aluno fez o agendamento do evento e "event_day" é dia do evento em si(que pode ser uma aula, visita ou ligação).
            """
            
            prompt = f"""
            Analise o seguinte texto sobre estratégia de envio de notificações e estruture-o conforme o modelo:
            
            {text}
            
            Identifique os horários de envio e mensagens para cada tipo de notificação mencionado no texto. 
            """
            
            notification_data = llm.get_response(system_prompt, prompt, NotificationSchedule, dump=True)
            logger.info("Resposta do LLM recebida com sucesso.")
            logger.info(f"tipo notification_data: {type(notification_data)}")
            logger.info(f"Dados estruturados: {notification_data}")
            
            return notification_data

        except Exception as e:
            logger.exception(f"Erro ao estruturar estratégia de notificação: {e}")
            return None

    def structure_phases_strategy(phase: str, query: str, bq) -> dict | None:
        """
        Utiliza LLM para criar uma estratégia de envio
        de mensagens após um contato de fase.
        """
        try:
            logger.info(
                "Criando estratégia de contatos pós contato p/ a fase: %s", phase
            )
            phase_data = bq.get_phase_context(phase_name=phase)
            phase_instruction = phase_data.get("instrucao_ia_fase", None)

            if not phase_instruction:
                logger.error("Não foi possível encontrar a fase")
                return None

            llm = OpenAIUtilModule(bq)

            system_prompt = """
Você é um agente especializado em entender as solicitações de usuários
 e criar uma estratégia de envios de mensagens,
 para melhorar a comunicação da empresa
 é imprescindível que você siga à risca a
 solicitação do nosso chefe, em relação aos dias
 em que serão enviadas as mensagens.\n
Crie instruções claras para o envio das mensagens.\n
Apenas leve em consideração instruções
 que falarem sobre **dias** depois, se forem solicitadas
 mensagens para horas ou minutos depois, ignore!
 A instrução deverá ser um texto explicando o contexto
 e o objetivo do contato.\n
Você irá receber também uma texto que irá
 te contextualizar melhor sobre a fase para
 a qual a estratégia será gerada.\n
**Contamos com você!**
            """.strip()

            prompt = f"""
Esta é a solicitação que chegou para você hoje:\n
```
{query}
```\n
Suas mensagens serão enviada para o aluno depois
 de um contato realizado com base nestas instruções:\n
```
{phase_instruction}
```\n
**Crie uma estratégia de contato precisa e detalhada!**
            """.strip()
            strategy = llm.get_response(
                system_prompt, prompt, PhaseScheduleStrategy, dump=False
            )

            return {
                "schema": [s.model_dump() for s in strategy.strategy]
            }

        except Exception as e:
            logger.exception(
                f"Erro ao estruturar estratégia de envios pós fase: {e}"
            )
            return None

    def resolve_schedules(
            data: dict, bq
    ) -> dict | None:
        """
        Resolve a geração de schedules com base nũa configuração solicitada
        """
        query = data.get("scheduler_text", None)
        resource = data.get("notification_type", None)

        if not all([query, resource]):
            return None

        match resource:
            case "phase_message_send":
                schedule = structure_phases_strategy(
                    data.get("category"), query, bq
                )
            case "book_class":
                schedule = structure_notification_strategy(
                    query, bq
                )
            case _:
                schedule = None

        return schedule
    
    def summarize_conversation(telefone: str, bq, id_conversa: str) -> dict:
        """
        Utiliza LLM para resumir uma conversa longa em um texto curto e objetivo, sem colocar informações sensíveis ou específicos.

        Args:
            text (str): Texto não estruturado contendo a estratégia de notificação.
            bq: Instância do BigQueryData para passar ao LLM.

        Returns:
            dict: Dados estruturados da estratégia de notificação.
        """
        try:
            llm = OpenAIUtilModule(bq)
            mensagens = bq.get_messages(telefone, id_conversa)
            mensagens_list = []
            for mensagem in mensagens.iterrows():
                mensagens_list.append({
                    "role": mensagem[1]["enviado_por"],
                    "content": mensagem[1]["mensagem"]
                })
            logger.info("Instância do LLM criada com sucesso.")
            
            system_prompt = """
### **Personagem**

Você é um assistente de IA especializado em resumir conversas de
atendimento ao cliente de um sistema de gestão.

### **Tarefa Principal**

Sua tarefa é analisar o histórico de uma conversa (em formato JSON) e
gerar um resumo conciso, objetivo e **completamente anônimo**. O resumo
deve capturar a intenção do cliente e as ações do assistente, focando
apenas no fluxo da conversa, sem se prender a detalhes específicos. O
resultado final deve ser um **texto corrido**, sem o uso de listas ou
marcadores.

### **Regras e Diretrizes**

1.  **Anonimização Total**: Esta é a regra mais importante. **Não
    mencione nomes de nada que exista**. Isso inclui:

      * Nomes de cidades, estados, bairros ou endereços.
      * Nomes de empresas, academias ou marcas.
      * Nomes de planos, aulas ou produtos.
      * Valores monetários.
      * Use descrições genéricas como "o cliente", "o assistente", "uma
        unidade próxima", "a localidade de interesse", "o plano com mais
        benefícios", etc.

2.  **Formato de Texto Corrido**: Apresente o resumo como um parágrafo
    único e coeso.

3.  **Foco no Fluxo da Conversa**: Concentre-se no motivo do contato,
    nas perguntas feitas pelo cliente e nas soluções ou informações
    oferecidas pelo assistente.

4.  **Ignore Mensagens de Sistema**: As mensagens com `"role": "system"`
    devem ser completamente ignoradas. Foque apenas na interação entre o
    `"user"` (cliente) e o `"assistant"` (assistente).

-----

### **Exemplo**

```json
[
    {
        "role": "user",
        "content": "Boa tarde!"
    },
    {
        "role": "assistant",
        "content": "Boa tarde! 😊\n\nSou um consultor da nossa rede. Seja muito bem-vindo! \n\nPara começar, você já é nosso cliente ou está interessado em se matricular?"
    },
    {
        "role": "user",
        "content": "não sou cliente"
    },
    {
        "role": "assistant",
        "content": "Entendi. Nós temos alguns planos disponíveis. \n\nSe você estiver interessado, posso te ajudar a encontrar uma unidade próxima e tirar suas dúvidas sobre os planos. Você tem alguma localização específica em mente?"
    },
    {
        "role": "user",
        "content": "[Localidade de Interesse]"
    },
    {
        "role": "assistant",
        "content": "Na região que você mencionou, temos uma unidade que pode ser uma boa opção. Aqui estão os detalhes:\n\n- *Nome:* [Nome da Unidade]\n- *Endereço:* [Endereço da Unidade]\n\nPosso te ajudar a entender melhor os planos de musculação disponíveis nesta unidade."
    },
    {
        "role": "user",
        "content": "Quero saber sobre os planos"
    },
    {
        "role": "assistant",
        "content": "Os planos disponíveis nesta unidade são:\n\n1. *Plano Básico*\n   - Benefício de primeiro mês\n   - Taxa de matrícula reduzida\n\n2. *Plano Completo*\n   - Acesso a todas as unidades da rede\n\n3. *Plano Anual*\n   - Contrato de longo prazo com desconto\n   - Isenção de taxa de adesão\n\nSe você estiver pronto para se matricular, basta usar o link correspondente. Estou aqui para ajudar com qualquer dúvida!"
    }
]
```

#### **Resumo Ideal Gerado (em texto corrido e anônimo)**

O cliente potencial entrou em contato buscando informações sobre como se
matricular. Ele informou não ser membro e especificou uma localidade de
interesse para treinar. Em resposta, o assistente indicou uma unidade
nas proximidades e, a pedido do cliente, detalhou os diferentes tipos de
planos disponíveis e seus benefícios gerais, orientando sobre como
proceder com a adesão.
            """
            
            prompt = f"""
            Resuma essa conversa:
            
            {json.dumps(mensagens_list, indent=2, ensure_ascii=False)}
            """
            
            resumo = llm.get_raw_response(system_prompt, prompt)
            
            return resumo

        except Exception as e:
            logger.exception(f"Erro ao resumir mensagem: {e}")
            return None
    

def get_name_matricula_context(context: dict) -> tuple[str, str]:
    """
    Extrai o nome e a matrícula do contexto fornecido.
    
    Args:
        context (dict): Dicionário contendo as informações do contexto.
        
    Returns:
        tuple: Tupla contendo o nome e a matrícula.
    """
    aluno = context.get("aluno", {})
    pessoa = aluno.get("pessoa", {})
    nome = str(pessoa.get("nome"))
    matricula = str(aluno.get("matricula"))
    return nome, matricula
