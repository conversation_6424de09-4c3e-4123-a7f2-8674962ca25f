import os
import json
import logging
import urllib.parse
import uuid
import re
import base64
import urllib
from datetime import datetime
import requests
from typing import (
    Literal,
    Optional,
    Union,
    Tuple
)

import ast
import pandas as pd
from pandas import DataFrame, Timestamp, concat
from psycopg2 import sql
from psycopg2.extras import <PERSON><PERSON>, DictCursor, execute_values
from google.cloud import bigquery


from src.connections.connections import Connections
from src.extras.util import (
    retry,
    parse_phone,
    get_date,
    log_func_time,
    DatabaseOpsTracer,
    trace_method,
)
from src.integrations.pacto.default_data.pacto_default_data import (
    PactoDefaultData as pdc,
)
from src.data.google_storage import Bucket

#NOTE: quando True, ativamos o modo Debug deste módulo para ver mais detalhes sobre a execução dos métodos contidos aqui
# o contrário seria False, no modo não verboso.
# A idéia é usar o decorador trace_method para isso como forma de rastrear nomes de métodos e seus caminhos específicos
# para capturar erros silenciosos nos logs, acontecendo no meio de um fluxo de comunicação IA <---Workers---> Humano
DEBUG_MODE_TRACE_METHODS = False

GCP_BIGQUERY_DATASET = os.getenv("GCP_BIGQUERY_DATASET")
DB_SCHEMA = os.getenv("DB_SCHEMA", "public")
BUCKET_NAME_CAMPANHA = os.getenv("BUCKET_NAME_CAMPANHA", "image_campanha")
if DB_SCHEMA == "development":
    BUCKET_NAME_CAMPANHA += "_dev"

# PostgreSQL Table Creation Recommendations:
#
# CREATE TABLE IF NOT EXISTS {DB_SCHEMA}.stickers (
#   id SERIAL PRIMARY KEY,
#   id_empresa VARCHAR(255) NOT NULL,
#   sticker TEXT NOT NULL,
#   data_ultima_atualizacao TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
#   CONSTRAINT stickers_id_empresa_unique UNIQUE (id_empresa)
# );
# CREATE INDEX IF NOT EXISTS idx_stickers_id_empresa ON {DB_SCHEMA}.stickers (id_empresa);
#
# CREATE TABLE IF NOT EXISTS {DB_SCHEMA}.conversas (
#   id SERIAL PRIMARY KEY,
#   id_conversa VARCHAR(255) NOT NULL,
#   enviado_por VARCHAR(50) NOT NULL,
#   id_usuario VARCHAR(255),
#   data_envio TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
#   mensagem TEXT NOT NULL,
#   status VARCHAR(50),
#   telefone VARCHAR(50) NOT NULL,
#   id_empresa VARCHAR(255) NOT NULL,
#   prompt_tokens INTEGER DEFAULT 0,
#   completion_tokens INTEGER DEFAULT 0,
#   Tipo_mensagem VARCHAR(50) DEFAULT 'text',
#   n_chars INTEGER DEFAULT 0,
#   n_seconds FLOAT DEFAULT 0,
#   model VARCHAR(50),
#   id_mensagem VARCHAR(255),
#   provedor_mensagem VARCHAR(50) DEFAULT 'z_api',
#   id_contexto VARCHAR(255),
#   situacao VARCHAR(255),
#   departamento VARCHAR(255),
#   colaborador VARCHAR(255)
# );
# CREATE INDEX IF NOT EXISTS idx_conversas_telefone ON {DB_SCHEMA}.conversas (telefone);
# CREATE INDEX IF NOT EXISTS idx_conversas_id_conversa ON {DB_SCHEMA}.conversas (id_conversa);
# CREATE INDEX IF NOT EXISTS idx_conversas_id_empresa ON {DB_SCHEMA}.conversas (id_empresa);
# CREATE INDEX IF NOT EXISTS idx_conversas_data_envio ON {DB_SCHEMA}.conversas (data_envio);
#
# CREATE TABLE IF NOT EXISTS {DB_SCHEMA}.contexto_campanhas (
#   id SERIAL PRIMARY KEY,
#   id_empresa VARCHAR(255) NOT NULL,
#   id_campanha VARCHAR(255) NOT NULL,
#   nome VARCHAR(255) NOT NULL,
#   instrucao TEXT,
#   keyword VARCHAR(255),
#   data_inicio TIMESTAMP WITH TIME ZONE,
#   data_fim TIMESTAMP WITH TIME ZONE,
#   imagem TEXT,
#   is_template BOOLEAN DEFAULT FALSE,
#   data_atualizacao TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
#   whatsapp_link TEXT,
#   CONSTRAINT campanha_id_empresa_id_campanha_unique UNIQUE (id_empresa, id_campanha)
# );
# CREATE INDEX IF NOT EXISTS idx_campanhas_id_empresa ON {DB_SCHEMA}.contexto_campanhas (id_empresa);
# CREATE INDEX IF NOT EXISTS idx_campanhas_id_campanha ON {DB_SCHEMA}.contexto_campanhas (id_campanha);
# CREATE INDEX IF NOT EXISTS idx_campanhas_data_inicio ON {DB_SCHEMA}.contexto_campanhas (data_inicio);
#
# CREATE TABLE IF NOT EXISTS {DB_SCHEMA}.logs_api (
#   id SERIAL PRIMARY KEY,
#   request_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
#   endpoint VARCHAR(255),
#   method VARCHAR(10),
#   status_code INTEGER,
#   response_time FLOAT,
#   id_empresa VARCHAR(255),
#   user_agent TEXT,
#   ip_address VARCHAR(50),
#   request_body JSONB,
#   response_body JSONB
# );
# CREATE INDEX IF NOT EXISTS idx_logs_api_request_time ON {DB_SCHEMA}.logs_api (request_time);
# CREATE INDEX IF NOT EXISTS idx_logs_api_id_empresa ON {DB_SCHEMA}.logs_api (id_empresa);

connections = Connections.get_instance()
logger = logging.getLogger("conversas_logger")


@retry(
    retries=3,
    delay=2,
    backoff=2,
    status_codes=(500, 404, 401, 402),
    exceptions=(requests.exceptions.RequestException, TypeError, Exception),
)
@DatabaseOpsTracer(
    span_name_prefix=__name__,
    span_description="Consultando instance_id e token da empresa no banco de dados",
    span_attributes={
        "id_empresa": "id_empresa",
    }
)
def get_from_empresa(id_empresa):
    cache_instance_id = redis_client_get(f"instances:{id_empresa}")
    if cache_instance_id:
        result_data = json.loads(cache_instance_id)
    else:
        conn = connections.postgres_connection
        cursor = conn.cursor(cursor_factory=DictCursor)
        query = f"SELECT instance_id, token FROM {DB_SCHEMA}.instances WHERE id_empresa = %s"
        cursor.execute(query, (id_empresa,))
        result = cursor.fetchone()
        cursor.close()

        if result:
            result_data = dict(result)
        else:
            result_data = None

    if not result_data:
        return None, None
    redis_client_set(
        f"instances:{id_empresa}",
        json.dumps(result_data),
        ex=8*60*60
    )
    instance_id, token = result_data['instance_id'], result_data['token']
    return instance_id, token


@retry(
    retries=3,
    delay=2,
    backoff=2,
    status_codes=(500, 404, 401, 402),
    exceptions=(requests.exceptions.RequestException, TypeError, Exception),
)
@DatabaseOpsTracer(
    span_name_prefix=__name__,
    span_description="Consultando instance_id e token da empresa no banco de dados",
)
def get_from_instance(instance_id):
    cache_instance_id = redis_client_get(f"empresas:{instance_id}")
    if cache_instance_id:
        result_data = json.loads(cache_instance_id)
    else:
        conn = connections.postgres_connection
        cursor = conn.cursor(cursor_factory=DictCursor)
        query = f"SELECT id_empresa, token FROM {DB_SCHEMA}.instances WHERE instance_id = %s"
        cursor.execute(query, (instance_id,))
        result = cursor.fetchone()
        cursor.close()

        if result:
            result_data = dict(result)
        else:
            result_data = None

    if not result_data:
        return None, None
    redis_client_set(
        f"empresas:{instance_id}",
        json.dumps(result_data),
        ex=8*60*60
    )
    id_empresa, token = result_data['id_empresa'], result_data['token']
    return id_empresa, token


@DatabaseOpsTracer(
    span_name_prefix=__name__,
    span_description="Consultando id_empresa no banco de dados"
)
def get_id_empresa():
    try:
        cache_id_empresa = redis_client_get("id_empresas_atualizaram_contexto:")
        if cache_id_empresa:
            logger.info("Obtendo id_empresa do cache...")
            return json.loads(cache_id_empresa)

        logger.info("Obtendo id_empresa do PostgreSQL...")
        conn = connections.postgres_connection
        cursor = conn.cursor()
        query = f"SELECT id_empresa FROM {DB_SCHEMA}.instances WHERE instance_id IS NOT NULL"
        cursor.execute(query)
        result = cursor.fetchall()
        cursor.close()

        if not result:
            return []

        lista_empresas = [row[0] for row in result if row[0] is not None]
        ex = int(os.getenv("MINUTES_FOR_CONTEXT_UPDATE", "10")) * 10
        redis_client_set(
            "id_empresas_atualizaram_contexto:", json.dumps(lista_empresas), ex=ex
        )
        return lista_empresas

    except Exception as e:
        logger.error(f"Erro ao buscar id_empresa: {e}")
        return []


@retry(
    retries=3,
    delay=2,
    backoff=2,
    status_codes=(500, 404, 401, 402),
    exceptions=(requests.exceptions.RequestException, TypeError, Exception),
)
@DatabaseOpsTracer(
    span_name_prefix=__name__,
    span_description="Consultando se é rede no banco de dados"
)
def is_rede(instance_id):
    cache_data = redis_client_get(f"is_rede:{instance_id}")
    if cache_data:
        result_data = json.loads(cache_data)
    else:
        conn = connections.postgres_connection
        cursor = conn.cursor(cursor_factory=DictCursor)
        query = f"SELECT is_rede FROM {DB_SCHEMA}.instances WHERE instance_id = %s"
        cursor.execute(query, (instance_id,))
        result = cursor.fetchone()
        cursor.close()

        if result:
            result_data = dict(result)
        else:
            result_data = None

    if not result_data:
        return None
    redis_client_set(
        f"is_rede:{instance_id}",
        json.dumps(result_data),
        ex=8*60*60
    )
    is_rede = result_data['is_rede']
    return is_rede


@retry(
    retries=3,
    delay=2,
    backoff=2,
    status_codes=(500, 404, 401, 402),
    exceptions=(requests.exceptions.RequestException, TypeError, Exception),
)
@DatabaseOpsTracer(
    span_name_prefix=__name__,
    span_description="Consultando id_empresa no banco de dados"
)
def get_from_telefone(telefone) -> str | None:
    cache_telefone = redis_client_get(f"empresas_telefone:{telefone}")
    if cache_telefone:
        result_data = cache_telefone.decode("utf-8")
        return result_data

    conn = connections.postgres_connection
    cursor = conn.cursor()
    query = f"SELECT id_empresa FROM {DB_SCHEMA}.contexto_academia WHERE telefone = %s"
    cursor.execute(query, (telefone,))
    result = cursor.fetchone()
    cursor.close()

    if not result:
        return None

    id_empresa = result_data['id_empresa']
    redis_client_set(
        f"empresas_telefone:{telefone}",
        id_empresa,
        ex=8*60*60
    )
    return id_empresa

@DatabaseOpsTracer(
    span_name_prefix=__name__,
    span_description="Consultando se a chave existe no banco de dados"
)
def check_if_key_exists(key):
    try:
        return connections.redis_client.exists(key)
    except Exception as e:
        logger.error(f"Error checking if key {key} exists: {e}")
        return False

@DatabaseOpsTracer(
    span_name_prefix=__name__,
    span_description="Obtendo valor da chave no banco de dados"
)
def redis_client_get(key):
    try:
        return connections.redis_client.get(key)
    except Exception as e:
        logger.error(f"Error getting key {key} from Redis: {e}")
        return None

@DatabaseOpsTracer(
    span_name_prefix=__name__,
    span_description="Salvando valor na chave no banco de dados"
)
def redis_client_set(key, value, ex=None):
    try:
        connections.redis_client.set(key, value, ex=ex)
    except Exception as e:
        logger.error(f"Error setting key {key} in Redis: {e}")


class PostgresDataHandler:
    def __init__(self, id_empresa, id_matriz=None):
        self.id_empresa = id_empresa
        self.id_matriz = id_matriz

    def set_id_empresa(self, id_empresa) -> None:
        self.id_empresa = id_empresa

    def _get_conn(self):
        return connections.postgres_connection

    def _get_bigquery_client(self):
        return connections.bigquery_client

    @retry(
        retries=3,
        delay=2,
        backoff=2,
        status_codes=(500, 404, 401, 402),
        exceptions=(requests.exceptions.RequestException, TypeError, Exception),
    )
    def _execute_query(self, query, extra_query="", use_id_empresa=True, params=None):
        conn = self._get_conn()
        cursor = conn.cursor(cursor_factory=DictCursor)

        if params is None:
            params = []

        if use_id_empresa:
            if "WHERE" in query:
                query = query + " AND id_empresa = %s"
                params.append(self.id_empresa)
            else:
                query = query + " WHERE id_empresa = %s"
                params.append(self.id_empresa)

        query += extra_query
        logger.info(
            f"Executando query:\n{query}"
        )  # Keep f-string as we need to format the query

        cursor.execute(query, params)

        # Create a result object similar to BigQuery for compatibility
        class PostgresResult:
            def __init__(self, cursor):
                self.cursor = cursor
                self.total_rows = cursor.rowcount

            def to_dataframe(self):
                if self.cursor.description is None:
                    return DataFrame()

                columns = [desc[0] for desc in self.cursor.description]
                data = self.cursor.fetchall()

                if not data:
                    return DataFrame(columns=columns)

                return DataFrame(data, columns=columns)

        result = PostgresResult(cursor)
        return result

    def _execute_query_no_error(
        self, query, extra_query="", use_id_empresa=True, params=None
    ):
        try:
            return self._execute_query(query, extra_query, use_id_empresa, params)
        except Exception as e:
            logger.error(f"Erro ao executar query:\n{query}\nErro: {e}")
            return None

    @retry(
        retries=3,
        delay=2,
        backoff=2,
        status_codes=(500, 404, 401, 402),
        exceptions=(requests.exceptions.RequestException, TypeError, Exception),
    )
    def _check_if_row_exists(
        self,
        table_id,
        id_column,
        id_value,
        column_type="string",
        use_id_empresa=False,
        use_id_matriz=False,
        extra_query="",
    ):
        try:
            params = []
            if column_type == "string":
                query = f"SELECT {id_column} FROM {table_id} WHERE {id_column} = %s"
                params.append(id_value)
            else:
                query = f"SELECT {id_column} FROM {table_id} WHERE {id_column} = %s"
                params.append(id_value)

            if extra_query != "":
                query += extra_query

            extra_params = []
            extra_q = ""
            if use_id_matriz:
                extra_q = " AND id_matriz = %s"
                extra_params.append(self.id_matriz)

            result = self._execute_query(
                query,
                extra_query=extra_q,
                use_id_empresa=use_id_empresa,
                params=params + extra_params,
            )

            logger.info(f"\nTotal de linhas encontradas: {result.total_rows}\n")
            return result.total_rows > 0
        except Exception as e:
            logger.error(f"Erro ao verificar se linha existe no PostgreSQL: {e}")
            return False

    @retry(
        retries=3,
        delay=2,
        backoff=2,
        status_codes=(500, 404, 401, 402),
        exceptions=(requests.exceptions.RequestException, TypeError, Exception),
    )
    def _save_dataframe_to_bq(self, query, job_config=None):
        conn = self._get_conn()
        cursor = conn.cursor()

        logger.info(f"Salvando dados no PostgreSQL: {query}")

        params = []
        if job_config and hasattr(job_config, "query_parameters"):
            # Convert BigQuery parameters to psycopg2 parameters
            query = query.replace("@", "%")
            for param in job_config.query_parameters:
                if param.type_ == "STRING":
                    params.append(param.value)
                elif param.type_ == "TIMESTAMP":
                    params.append(param.value)
                elif param.type_ == "INT64":
                    params.append(param.value)
                elif param.type_ == "FLOAT64":
                    params.append(param.value)
                elif param.type_ == "BOOL":
                    params.append(param.value)
                elif param.type_ == "JSON":
                    params.append(Json(param.value))
                else:
                    params.append(param.value)

        cursor.execute(query, params)
        conn.commit()

        # Create a result object similar to BigQuery for compatibility
        class PostgresResult:
            def __init__(self, cursor):
                self.cursor = cursor
                self.total_rows = cursor.rowcount

            def to_dataframe(self):
                if self.cursor.description is None:
                    return DataFrame()

                columns = [desc[0] for desc in self.cursor.description]
                data = self.cursor.fetchall()

                if not data:
                    return DataFrame(columns=columns)

                return DataFrame(data, columns=columns)

        result = PostgresResult(cursor)
        return result

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def create_empresa(self, data) -> None:
        try:
            logger.info("Criando empresa...")
            table_id = f"{DB_SCHEMA}.empresas"
            dados_json = json.dumps(data, ensure_ascii=False)
            data_atualizacao = Timestamp.now()
            query = f"""INSERT INTO {table_id} (contexto_empresa_json, data_ultima_atualizacao, id_empresa) VALUES (%s, %s, %s)"""
            conn = self._get_conn()
            cursor = conn.cursor()
            cursor.execute(query, (dados_json, data_atualizacao, self.id_empresa))
            conn.commit()
            cursor.close()

            redis_client_set(
                f"empresas:{self.id_empresa}",
                dados_json,
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao criar empresa: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def update_empresa(self, data) -> None:
        try:
            logger.info("Atualizando empresa...")
            table_id = f"{DB_SCHEMA}.empresas"
            dados_json = json.dumps(data, ensure_ascii=False)
            data_atualizacao = Timestamp.now()
            query = f"""UPDATE {table_id} SET contexto_empresa_json = %s, data_ultima_atualizacao = %s WHERE id_empresa = %s"""
            conn = self._get_conn()
            cursor = conn.cursor()
            cursor.execute(query, (dados_json, data_atualizacao, self.id_empresa))
            conn.commit()
            cursor.close()

            redis_client_set(
                f"empresas:{self.id_empresa}",
                dados_json,
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao atualizar empresa: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def patch_empresa(self, data) -> None:
        try:
            current_data = self.get_empresa()
            for key in data.keys():
                if key in current_data:
                    current_data[key] = data[key]

            self.update_empresa(current_data)
        except Exception as e:
            logger.error(f"Erro ao atualizar empresa: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def get_empresa(self) -> dict:
        try:
            cache_empresa = redis_client_get(f"empresas:{self.id_empresa}")
            if cache_empresa:
                logger.info("Obtendo empresa do cache...")
                return json.loads(cache_empresa)
            logger.info("Obtendo empresa...")
            result = self._execute_query(
                f"SELECT contexto_empresa_json FROM {DB_SCHEMA}.empresas"
            )
            result = result.to_dataframe()
            if result.empty:
                return {}
            empresa = result.iloc[0]["contexto_empresa_json"]
            if isinstance(empresa, str):
                return json.loads(empresa)
            return empresa
        except Exception as e:
            logger.error(f"Erro ao obter empresa: {e}")
            return {}

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def delete_empresa(self) -> None:
        try:
            logger.info("Deletando empresa...")
            table_id = f"{DB_SCHEMA}.empresas"
            query = f"DELETE FROM {table_id} WHERE id_empresa = %s"

            conn = self._get_conn()
            cursor = conn.cursor()
            cursor.execute(query, (self.id_empresa,))
            conn.commit()
            cursor.close()

            connections.redis_client.delete(f"empresas:{self.id_empresa}")
        except Exception as e:
            logger.error(f"Erro ao deletar empresa: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def create_sticker(self, data: dict):
        """Cria um sticker no PostgreSQL e no Redis."""
        try:
            table_id = f"{DB_SCHEMA}.stickers"
            if not self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                redis_client_set(
                    f"sticker-{self.id_empresa}",
                    data.get("sticker"),
                    ex=8*60*60
                )
                logger.info("Criando sticker...")
                logger.info("Data: %s", data)
                data_atualizacao = Timestamp.now()
                query = f"""
                    INSERT INTO {table_id} (sticker, data_ultima_atualizacao, id_empresa)
                    VALUES (%s, %s, %s)
                """

                conn = self._get_conn()
                cursor = conn.cursor()
                cursor.execute(
                    query, (str(data.get("sticker")), data_atualizacao, self.id_empresa)
                )
                conn.commit()
                cursor.close()
        except Exception as e:
            logger.error("Erro ao criar sticker: %s", e)

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def update_sticker(self, data):
        """Atualiza um sticker no PostgreSQL e no Redis.

        Recomendação de índice para a tabela stickers:
        CREATE INDEX IF NOT EXISTS idx_stickers_id_empresa ON {DB_SCHEMA}.stickers (id_empresa);
        """
        try:
            table_id = f"{DB_SCHEMA}.stickers"
            sticker_value = str(data.get("sticker", ""))

            # Salva no Redis independente do resultado do banco para melhor performance
            redis_client_set(
                f"sticker-{self.id_empresa}",
                data.get("sticker"),
                ex=8*60*60
            )
            logger.info("Atualizando sticker...")

            data_atualizacao = Timestamp.now()

            conn = self._get_conn()
            cursor = conn.cursor()

            # Usa upsert pattern para simplificar a lógica e melhorar performance
            # Esta abordagem é mais eficiente que verificar a existência e depois inserir/atualizar
            query = f"""
                INSERT INTO {table_id} (id_empresa, sticker, data_ultima_atualizacao)
                VALUES (%s, %s, %s)
                ON CONFLICT (id_empresa) 
                DO UPDATE SET 
                    sticker = EXCLUDED.sticker,
                    data_ultima_atualizacao = EXCLUDED.data_ultima_atualizacao
            """

            cursor.execute(query, (self.id_empresa, sticker_value, data_atualizacao))

            conn.commit()
            cursor.close()
            logger.info("Sticker atualizado com sucesso.")

        except Exception as e:
            logger.error("Erro ao atualizar sticker: %s", e)
            # Tenta recuperar se ocorrer um erro
            try:
                if conn and not conn.closed:
                    conn.rollback()
                    cursor.close()
            except Exception as e:
                logger.error(f"Erro ao fazer rollback da transação: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def get_sticker(self) -> str:
        """Obtém um sticker do PostgreSQL ou do Redis."""
        try:
            if check_if_key_exists(f"sticker-{self.id_empresa}"):
                return redis_client_get(f"sticker-{self.id_empresa}")
            logger.info("Obtendo sticker...")
            result = self._execute_query(f"SELECT sticker FROM {DB_SCHEMA}.stickers")
            result = result.to_dataframe()
            if result.empty:
                return None
            return result.iloc[0]["sticker"]
        except Exception as e:
            logger.error("Erro ao obter sticker: %s", e)
            return None

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def delete_sticker(self) -> None:
        """Deleta um sticker do PostgreSQL e do Redis."""
        try:
            if check_if_key_exists(f"sticker-{self.id_empresa}"):
                connections.redis_client.delete(f"sticker-{self.id_empresa}")
            logger.info("Deletando sticker...")
            table_id = f"{DB_SCHEMA}.stickers"

            conn = self._get_conn()
            cursor = conn.cursor()

            query = f"DELETE FROM {table_id} WHERE id_empresa = %s"
            cursor.execute(query, (self.id_empresa,))

            conn.commit()
            cursor.close()
        except Exception as e:
            logger.error("Erro ao deletar sticker: %s", e)

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def get_gym_context(self) -> dict:
        if not hasattr(self, 'id_empresa') or not self.id_empresa:
            logger.error("get_gym_context chamado sem id_empresa no handler.")
            return {}

        cache_key = f"gym_context-{self.id_empresa}"
        try:
            if cache_gym_context := redis_client_get(cache_key):
                logger.info("Obtendo contexto da academia do cache...")
                return json.loads(cache_gym_context)

            logger.info("Obtendo contexto da academia do PostgreSQL...")
            
            query = f"SELECT contexto_academia_json FROM {DB_SCHEMA}.contexto_academia WHERE id_empresa = '{self.id_empresa}'"
            result_df = self._execute_query(query).to_dataframe()

            if result_df.empty:
                logger.warning(f"Nenhum contexto de academia encontrado para a empresa {self.id_empresa}.")
                return {}

            gym_context = result_df.iloc[0]["contexto_academia_json"]
            if not gym_context or isinstance(gym_context, type(None)):
                logger.warning(f"Contexto da academia é nulo no banco para a empresa {self.id_empresa}.")
                return {}

            gym_context_json = json.loads(gym_context) if isinstance(gym_context, str) else gym_context

            redis_client_set(
                cache_key,
                json.dumps(gym_context_json), # garante que está salvando uma string
                ex=8*60*60
            )

            return gym_context_json

        except Exception as e:
            # O except agora é uma rede de segurança final
            logger.error(f"Erro inesperado ao obter contexto da academia para {self.id_empresa}: {e}", exc_info=True)
            return {} # Garante que em QUALQUER falha, o retorno é seguro

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def update_gym_context(self, context) -> None:
        try:
            logger.info("Atualizando contexto da academia...")
            table_id = f"{DB_SCHEMA}.contexto_academia"
            dados_json = json.dumps(context, ensure_ascii=False)
            data_atualizacao = Timestamp.now()
            id_empresa = self.id_empresa

            conn = self._get_conn()
            cursor = conn.cursor()

            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                query = f"""
                    UPDATE {table_id}
                    SET contexto_academia_json = %s,
                        data_ultima_atualizacao = %s
                    WHERE id_empresa = %s
                """
                cursor.execute(query, (dados_json, data_atualizacao, id_empresa))
            else:
                query = f"""
                    INSERT INTO {table_id} (contexto_academia_json, data_ultima_atualizacao, id_empresa)
                    VALUES (%s, %s, %s)
                """
                cursor.execute(query, (dados_json, data_atualizacao, id_empresa))

            conn.commit()
            cursor.close()

            redis_client_set(
                f"gym_context-{self.id_empresa}",
                dados_json,
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao atualizar contexto da academia: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def get_plans_context(self) -> Optional[dict]:
        try:
            cache_plans_context = redis_client_get(f"plans_context-{self.id_empresa}")
            if cache_plans_context:
                logger.info("Obtendo contexto dos planos do cache...")
                return json.loads(cache_plans_context)

            logger.info("Obtendo contexto dos planos do PostgreSQL...")
            result = self._execute_query(f"SELECT * FROM {DB_SCHEMA}.contexto_planos")
            result_data = result.to_dataframe()
            result_data_json = result_data["planos_json"].iloc[0]

            if isinstance(result_data_json, str):
                plans_json = result_data_json
            else:
                plans_json = json.dumps(result_data_json)

            redis_client_set(
                f"plans_context-{self.id_empresa}",
                result_data_json,
                ex=8*60*60
            )
            return json.loads(plans_json)
        except Exception as e:
            logger.error(f"Erro ao obter contexto dos planos: {e}")
            return None

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def update_plans_context(self, planos) -> None:
        try:
            logger.info("Atualizando contexto dos planos...")
            table_id = f"{DB_SCHEMA}.contexto_planos"
            planos_json = json.dumps(planos)
            data_atualizacao = Timestamp.now()

            conn = self._get_conn()
            cursor = conn.cursor()

            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                query = f"UPDATE {table_id} SET planos_json = %s, data_ultima_atualizacao = %s WHERE id_empresa = %s"
                cursor.execute(query, (planos_json, data_atualizacao, self.id_empresa))
            else:
                query = f"INSERT INTO {table_id} (planos_json, data_ultima_atualizacao, id_empresa) VALUES (%s, %s, %s)"
                cursor.execute(query, (planos_json, data_atualizacao, self.id_empresa))

            conn.commit()
            cursor.close()

            # Save to cache
            redis_client_set(
                f"plans_context-{self.id_empresa}",
                json.dumps(planos),
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao atualizar contexto dos planos: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def get_phase_context(self, phase_name=None, get_all=False):
        try:
            cache_key = f"phases_context-{self.id_empresa}"
            cache_phases_context = redis_client_get(cache_key)
            if cache_phases_context:
                logger.info("Obtendo contexto das fases do cache...")
                phases_context = json.loads(cache_phases_context)
                if get_all:
                    return phases_context
                if not phase_name and len(phases_context) > 0:
                    return phases_context[0]
                for phase in phases_context:
                    if phase["nome_fase"] == phase_name:
                        return phase

            logger.info("Obtendo contexto das fases do PostgreSQL...")
            query = f"SELECT * FROM {DB_SCHEMA}.contexto_fases"
            params = []

            if phase_name:
                query += " WHERE nome_fase = %s"
                params.append(phase_name)

            extra_query = "" if get_all else " LIMIT 1"

            conn = self._get_conn()
            cursor = conn.cursor(cursor_factory=DictCursor)
            cursor.execute(query + extra_query, params)

            results = cursor.fetchall()
            if not results:
                phase = (
                    getattr(pdc.crm_phase, phase_name)
                    if phase_name
                    else pdc.crm_phase.LEADS_HOJE
                )
                result_data_json = []
            else:
                # Convert results to a list of dictionaries
                result_data_json = [dict(row) for row in results]
                phase = (
                    result_data_json[0]
                    if result_data_json
                    else getattr(pdc.crm_phase, phase_name)
                    if phase_name
                    else pdc.crm_phase.LEADS_HOJE
                )

            cursor.close()

            redis_client_set(
                cache_key,
                json.dumps(result_data_json),
                ex=8*60*60
            )
            if get_all:
                return result_data_json
            else:
                return phase
        except Exception as e:
            logger.error(f"Erro ao obter contexto das fases: {e}")
            return pdc.crm_phase.LEADS_HOJE

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def update_phases_context(self, context) -> None:
        try:
            logger.info("Atualizando contexto das fases...")
            table_id = f"{DB_SCHEMA}.contexto_fases"
            df = DataFrame(
                {
                    "codigo_fase": [fase.get("codigo", -1) for fase in context],
                    "nome_fase": [fase["name"] for fase in context],
                    "descricao_fase": [fase["descricao"] for fase in context],
                    "instrucao_ia_fase": [fase["instrucao_ia"] for fase in context],
                    "data_ultima_atualizacao": Timestamp.now(),
                    "id_empresa": [self.id_empresa for _ in context],
                }
            )

            conn = self._get_conn()
            cursor = conn.cursor()

            for fase in context:
                data_atualizacao = Timestamp.now()
                codigo_fase = int(fase.get("codigo", -1))
                nome_fase = fase["name"]
                descricao_fase = fase["descricao"]
                instrucao_ia_fase = fase["instrucao_ia"]

                if self._check_if_row_exists(
                    table_id, "nome_fase", nome_fase, use_id_empresa=True
                ):
                    query = f"""
                    UPDATE {table_id} 
                    SET 
                    codigo_fase = %s,
                    nome_fase = %s,
                    descricao_fase = %s,
                    instrucao_ia_fase = %s,
                    data_ultima_atualizacao = %s
                    WHERE nome_fase = %s AND id_empresa = %s"""
                    cursor.execute(
                        query,
                        (
                            codigo_fase,
                            nome_fase,
                            descricao_fase,
                            instrucao_ia_fase,
                            data_atualizacao,
                            nome_fase,
                            self.id_empresa,
                        ),
                    )
                else:
                    query = f"INSERT INTO {table_id} (codigo_fase, nome_fase, descricao_fase, instrucao_ia_fase, data_ultima_atualizacao, id_empresa) VALUES (%s, %s, %s, %s, %s, %s)"
                    cursor.execute(
                        query,
                        (
                            codigo_fase,
                            nome_fase,
                            descricao_fase,
                            instrucao_ia_fase,
                            data_atualizacao,
                            self.id_empresa,
                        ),
                    )

            conn.commit()
            cursor.close()

            phases_context = redis_client_get(f"phases_context-{self.id_empresa}")

            if phases_context:
                phases_context = json.loads(phases_context)
                df = concat([DataFrame(phases_context), df]).drop_duplicates(
                    subset=["codigo_fase"], keep="last"
                )

            redis_client_set(
                f"phases_context-{self.id_empresa}",
                json.loads(json.dumps(df.to_json(orient='records'))),
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao atualizar contexto das fases: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def get_classes_context(self) -> Optional[dict]:
        try:
            cache_classes_context = redis_client_get(
                f"classes_context-{self.id_empresa}"
            )
            if cache_classes_context:
                logger.info("Obtendo contexto das turmas do cache...")
                return json.loads(cache_classes_context)

            logger.info("Obtendo contexto das turmas do PostgreSQL...")
            result = self._execute_query(f"SELECT * FROM {DB_SCHEMA}.contexto_turmas")
            result_data = result.to_dataframe()
            result_data_json = result_data["turmas"].iloc[0]

            if isinstance(result_data_json, str):
                turmas_json = result_data_json
            else:
                turmas_json = json.dumps(result_data_json)

            redis_client_set(
                f"classes_context-{self.id_empresa}",
                result_data_json,
                ex=8*60*60
            )

            return json.loads(turmas_json)
        except Exception as e:
            logger.error(f"Erro ao obter contexto das turmas: {e}")
            return None

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def update_classes_context(self, context) -> None:
        try:
            logger.info("Atualizando contexto das turmas...")
            table_id = f"{DB_SCHEMA}.contexto_turmas"
            context_json = json.dumps(context)
            data_atualizacao = Timestamp.now()

            conn = self._get_conn()
            cursor = conn.cursor()

            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                query = f"UPDATE {table_id} SET turmas = %s, data_ultima_atualizacao = %s WHERE id_empresa = %s"
                cursor.execute(query, (context_json, data_atualizacao, self.id_empresa))
            else:
                query = f"INSERT INTO {table_id} (turmas, data_ultima_atualizacao, id_empresa) VALUES (%s, %s, %s)"
                cursor.execute(query, (context_json, data_atualizacao, self.id_empresa))

            conn.commit()
            cursor.close()

            # Save to cache
            redis_client_set(
                f"classes_context-{self.id_empresa}",
                json.dumps(context),
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao atualizar contexto das turmas: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def get_products_context(self) -> Optional[dict]:
        try:
            cache_products_context = redis_client_get(
                f"products_context-{self.id_empresa}"
            )
            if cache_products_context:
                logger.info("Obtendo contexto dos produtos do cache...")
                if isinstance(cache_products_context, bytes):
                    cache_products_context = cache_products_context.decode("utf-8")
                try:
                    return json.loads(cache_products_context)
                except json.JSONDecodeError:
                    return ast.literal_eval(cache_products_context)

            logger.info("Obtendo contexto dos produtos do PostgreSQL...")
            result = self._execute_query(f"SELECT * FROM {DB_SCHEMA}.contexto_produtos")
            result_data = result.to_dataframe()
            result_data_json = result_data["produtos"].iloc[0]

            if isinstance(result_data_json, str):
                produtos_json = result_data_json
            else:
                produtos_json = json.dumps(result_data_json)

            redis_client_set(
                f"products_context-{self.id_empresa}",
                result_data_json,
                ex=8*60*60
            )

            try:
                return json.loads(produtos_json)
            except json.JSONDecodeError:
                return ast.literal_eval(produtos_json)

        except Exception as e:
            logger.error(f"Erro ao obter contexto dos produtos: {e}")
            return None

    def update_products_context(self, context) -> None:
        try:
            logger.info("Atualizando contexto dos produtos...")
            table_id = f"{DB_SCHEMA}.contexto_produtos"
            data_atualizacao = Timestamp.now()

            if isinstance(context, dict) or isinstance(context, list):
                context_json = json.dumps(context)
            else:
                context_json = context

            conn = self._get_conn()
            cursor = conn.cursor()

            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                query = f"UPDATE {table_id} SET produtos = %s, data_ultima_atualizacao = %s WHERE id_empresa = %s"
                cursor.execute(query, (context_json, data_atualizacao, self.id_empresa))
            else:
                query = f"INSERT INTO {table_id} (produtos, data_ultima_atualizacao, id_empresa) VALUES (%s, %s, %s)"
                cursor.execute(query, (context_json, data_atualizacao, self.id_empresa))

            conn.commit()
            cursor.close()

            redis_client_set(
                f"products_context-{self.id_empresa}",
                json.dumps(context),
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao atualizar contexto dos produtos: {e}")
    
    #NOTE: isolei essa lógica de procura de fases no JSON pra entender melhor ela (SRP - S do SOLID)
    def _get_fase_from_context(self, context: dict) -> str:
        """
        Função auxiliar para extrair a fase de um dicionário de contexto
        com estrutura inconsistente.
        """
        if not context:
            return "LEADS_HOJE"
            
        fase = (
            context.get("fase_crm")
            or context.get("aluno", {}).get("fase_crm")
            or context.get("fase_atual")
            or "LEADS_HOJE"
        )
        return fase

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    #NOTE: função para pegar o contexto do usuário mais limpa, refatorada e pythonica
    def get_user_context(
            self,
            telefone,
            use_id_matriz=False,
            use_id_empresa=True
        ) -> tuple[dict, str]:
        telefone = str(telefone)
        try:
            cache_key = f"{telefone}-{self.id_empresa}" if use_id_empresa else f"{telefone}-{self.id_matriz}"
            cache_user_context = redis_client_get(cache_key)
            if cache_user_context:
                logger.info("Obtendo contexto do usuário do cache...")
                contexto = json.loads(cache_user_context)
                fase_atual = self._get_fase_from_context(contexto)
                return contexto, fase_atual

            logger.info("Obtendo contexto do usuário do PostgreSQL...")
            query = f"SELECT * FROM {DB_SCHEMA}.contexto_usuario WHERE telefone = %s"
            params = [telefone]

            extra_query = ""
            if use_id_matriz:
                extra_query = " AND id_matriz = %s"
                params.append(self.id_matriz)

            result = self._execute_query(
                query=query,
                extra_query=extra_query,
                use_id_empresa=use_id_empresa,
                params=params,
            )
            result_data = result.to_dataframe()

            if result_data.empty:
                return None, None

            result_data_row = result_data.iloc[0]

            if isinstance(result_data_row["contexto_usuario_json"], str):
                contexto_do_banco = json.loads(result_data_row["contexto_usuario_json"])
            else:
                contexto_do_banco = result_data_row["contexto_usuario_json"]

            fase_atual = self._get_fase_from_context(contexto_do_banco)
            
            contexto_normalizado = contexto_do_banco.copy()
            contexto_normalizado["fase_atual"] = fase_atual
            
            contexto_normalizado.pop("fase_crm", None)
            if "aluno" in contexto_normalizado:
                contexto_normalizado["aluno"].pop("fase_crm", None)
                contexto_normalizado["aluno"].pop("fase_atual", None)
                
            redis_client_set(
                cache_key,
                json.dumps(contexto_normalizado),
                ex=8*60*60
            )
            return contexto_normalizado, fase_atual
            
        except Exception as e:
            logger.error(f"Erro ao obter contexto do usuário: {e}")
            return None, None

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def get_id_empresa_from_matriz(self, telefone):
        try:
            cache = redis_client_get(f"save_empresa:{self.id_matriz}-{telefone}")

            if cache:
                return cache.decode("utf-8")

            conn = self._get_conn()
            cursor = conn.cursor()

            query = f"SELECT id_empresa FROM {DB_SCHEMA}.contexto_usuario WHERE id_matriz = %s AND telefone = %s"
            cursor.execute(query, (self.id_matriz, telefone))

            result = cursor.fetchone()
            cursor.close()

            if not result:
                return None

            id_empresa = result[0]
            if id_empresa is None or id_empresa == "None":
                return None

            redis_client_set(f"save_empresa:{self.id_matriz}-{telefone}", id_empresa)
            return id_empresa
        except Exception as e:
            logger.error(f"Erro ao obter id_empresa da matriz: {e}")
            return None

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def save_user_context(self, context, telefone, fase, origin="") -> None:
        if context == {} or not context:
            return
        telefone = str(telefone)
        # add phase to context
        if isinstance(context, str):
            context = json.loads(context)
        context["fase_atual"] = fase
        context_json = json.dumps(context).encode("utf-8").decode("utf-8")
        try:
            logger.info("Salvando contexto do usuário...")
            table_id = f"{DB_SCHEMA}.contexto_usuario"
            # use_id_matriz=True porque se não for uma rede, o id_empresa é o mesmo que o id_matriz
            id_usuario_unico = (
                f"{telefone}-{self.id_matriz}"
                if self.id_matriz
                else f"{telefone}-{self.id_empresa}"
            )
            data_atualizacao = Timestamp.now()

            conn = self._get_conn()
            cursor = conn.cursor()

            if self._check_if_row_exists(
                table_id, "id_usuario_unico", id_usuario_unico
            ):
                query = f"""
                UPDATE {table_id} 
                SET id_usuario_unico = %s, 
                    contexto_usuario_json = %s, 
                    data_ultima_atualizacao = %s, 
                    fase_atual = %s 
                WHERE telefone = %s AND id_matriz = %s
                """
                cursor.execute(
                    query,
                    (
                        id_usuario_unico,
                        context_json,
                        data_atualizacao,
                        fase,
                        telefone,
                        self.id_matriz,
                    ),
                )
            else:
                query = f"""
                INSERT INTO {table_id} 
                (id_usuario_unico, id_usuario, contexto_usuario_json, data_ultima_atualizacao, telefone, id_empresa, fase_atual, id_matriz) 
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(
                    query,
                    (
                        id_usuario_unico,
                        "",
                        context_json,
                        data_atualizacao,
                        telefone,
                        self.id_empresa,
                        fase,
                        self.id_matriz,
                    ),
                )

            conn.commit()
            cursor.close()

            # Save to cache
            if self.id_empresa:
                context = json.loads(context) if isinstance(context, str) else context
                context = {
                    'origin_last_update': origin,
                    **context
                }
                context = json.dumps(context).encode('utf-8').decode('utf-8')
                redis_client_set(
                    f"{telefone}-{self.id_empresa}",
                    context,
                    ex=8*60*60
                )
        except Exception as e:
            logger.error(f"Erro ao salvar contexto do usuário: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def save_user_origin(self, origin: str, telefone: str, id_empresa: str) -> None:
        """Salva a origem do lead no PostgreSQL"""

        try:
            logger.info("Salvando origem do usuário...")
            table_id = f"{DB_SCHEMA}.contexto_usuario"
            data_atualizacao = Timestamp.now()

            conn = self._get_conn()
            cursor = conn.cursor()

            query = f"UPDATE {table_id} SET origin = %s, data_ultima_atualizacao = %s WHERE telefone = %s"
            cursor.execute(query, (origin, data_atualizacao, telefone))

            conn.commit()
            cursor.close()
        except Exception as e:
            logger.error(f"Erro ao salvar a origem do lead: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def save_user_empresa(self, id_empresa, context, telefone, fase) -> None:
        telefone = str(telefone)
        # add phase to context
        try:
            logger.info("Salvando empresa do usuário...")
            table_id = f"{DB_SCHEMA}.contexto_usuario"
            data_atualizacao = Timestamp.now()
            id_usuario_unico = f"{telefone}-{id_empresa}"

            if isinstance(context, dict) or isinstance(context, list):
                context_json = json.dumps(context)
            else:
                context_json = context

            conn = self._get_conn()
            cursor = conn.cursor()

            logger.info("Verificando se o usuário já existe na base de dados")
            if self._check_if_row_exists(
                table_id, "telefone", telefone, use_id_matriz=True
            ):
                query = f"""
                UPDATE {table_id} 
                SET id_usuario_unico = %s,
                    id_empresa = %s, 
                    data_ultima_atualizacao = %s, 
                    fase_atual = %s, 
                    contexto_usuario_json = %s 
                WHERE telefone = %s AND id_matriz = %s
                """
                cursor.execute(
                    query,
                    (
                        id_usuario_unico,
                        id_empresa,
                        data_atualizacao,
                        fase,
                        context_json,
                        telefone,
                        self.id_matriz,
                    ),
                )
            else:
                query = f"""
                INSERT INTO {table_id} 
                (id_usuario_unico, id_usuario, contexto_usuario_json, data_ultima_atualizacao, telefone, id_empresa, fase_atual, id_matriz) 
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(
                    query,
                    (
                        id_usuario_unico,
                        "",
                        context_json,
                        data_atualizacao,
                        telefone,
                        id_empresa,
                        fase,
                        self.id_matriz,
                    ),
                )

            conn.commit()
            cursor.close()

            redis_client_set(f"save_empresa:{self.id_matriz}-{telefone}", id_empresa)
            # Save to cache
        except Exception as e:
            logger.error(f"Erro ao salvar empresa do usuário: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def get_personality_context(self) -> str:
        try:
            cache_personality_context = redis_client_get(
                f"personality_context-{self.id_empresa}"
            )
            if cache_personality_context:
                logger.info("Obtendo contexto da personalidade do cache...")
                return json.loads(cache_personality_context).get("personalidade")

            logger.info("Obtendo contexto da personalidade do PostgreSQL...")

            result = self._execute_query(
                f"SELECT personalidade FROM {DB_SCHEMA}.contexto_personalidade"
            )
            result_dataframe = result.to_dataframe()
            if result_dataframe.empty:
                personality_context = pdc.personality
            else:
                personality_context = result_dataframe.iloc[0].values[0]
            
            redis_client_set(
                f"personality_context-{self.id_empresa}",
                json.dumps({"personalidade": personality_context}),
                ex=8*60*60
            )
            return personality_context
        except Exception as e:
            logger.error(f"Erro ao obter contexto da personalidade: {e}")
            return pdc.personality

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def update_personality_context(self, context) -> None:
        try:
            logger.info("Atualizando contexto da personalidade...")
            table_id = f'"{DB_SCHEMA}"."contexto_personalidade"'
            personalidade_json = json.dumps(context.get("personalidade"))
            data_atualizacao = datetime.now()
            id_empresa = self.id_empresa

            conn = self._get_conn()
            cursor = conn.cursor()

            if self._check_if_row_exists(table_id, "id_empresa", id_empresa):
                query = f"UPDATE {table_id} SET personalidade = %s, data_ultima_atualizacao = %s WHERE id_empresa = %s"
                params = (personalidade_json, data_atualizacao, id_empresa)
            else:
                query = f"INSERT INTO {table_id} (personalidade, data_ultima_atualizacao, id_empresa) VALUES (%s, %s, %s)"
                params = (personalidade_json, data_atualizacao, id_empresa)
            
            cursor.execute(query, params)
            
            #NOTE: 
            #CORREÇÃO: A chamada duplicada para _save_dataframe_to_bq foi removida.
            conn.commit()
            cursor.close()

            redis_client_set(
                f"personality_context-{id_empresa}",
                json.dumps(context),
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao atualizar contexto da personalidade: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def save_message(
        self,
        sent_by,
        message,
        telefone,
        model="",
        message_type="text",
        status="",
        prompt_tokens=0,
        completion_tokens=0,
        n_chars=0,
        n_seconds=0,
        provider="z_api",
        message_id=None,
        id_contexto=None,
        situacao=None,
        departamento=None,
        colaborador=None,
        roles_to_save_redis=["user", "assistant"],
    ) -> None:
        id_conversa = redis_client_get(
            f"current_conversation:{telefone}-{self.id_empresa}"
        )

        if id_conversa:
            id_conversa = id_conversa.decode("utf-8")
        else:
            id_conversa = str(uuid.uuid4())
            redis_client_set(
                f"current_conversation:{telefone}-{self.id_empresa}", id_conversa
            )
        if not isinstance(message, str):
            message = f"{message}"

        telefone = str(telefone)
        logger.info("Salvando mensagem...")
        try:
            if sent_by in roles_to_save_redis:
                # last messages cache
                last_messages = redis_client_get(
                    f"last_messages-{telefone}-{self.id_empresa}"
                )

                if last_messages:
                    last_messages = json.loads(last_messages)
                    last_messages += [{"enviado_por": sent_by, "mensagem": message}]
                else:
                    last_messages = [{"enviado_por": sent_by, "mensagem": message}]

                if self.id_empresa is None:
                    redis_client_set(
                        f"last_messages-{telefone}-{self.id_matriz}",
                        json.dumps(last_messages),
                        ex=8*60*60
                    )
                else:
                    redis_client_set(
                        f"last_messages-{telefone}-{self.id_empresa}",
                        json.dumps(last_messages),
                        ex=8*60*60
                    )

            table_id = f"{DB_SCHEMA}.conversas"
            query = f"""
                INSERT INTO {table_id} 
                (id_conversa, enviado_por, id_usuario, data_envio, mensagem, status, telefone, id_empresa, prompt_tokens, completion_tokens, Tipo_mensagem, n_chars, n_seconds, model, id_mensagem, provedor_mensagem, id_contexto, situacao, departamento, colaborador) 
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            conn = self._get_conn()
            cursor = conn.cursor()
            cursor.execute(
                query,
                (
                    id_conversa,
                    sent_by,
                    "",
                    Timestamp.now(),
                    message,
                    status,
                    telefone,
                    self.id_empresa,
                    prompt_tokens,
                    completion_tokens,
                    message_type,
                    n_chars,
                    n_seconds,
                    model,
                    message_id,
                    provider,
                    id_contexto,
                    situacao,
                    departamento,
                    colaborador,
                ),
            )
            conn.commit()
            cursor.close()

        except Exception as e:
            logger.error(f"Erro ao salvar mensagem: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def save_instance_data(self, instance_id, token) -> None:
        try:
            logger.info("Salvando dados da instância...")

            old_id_empresa, _ = get_from_instance(instance_id)
            old_instance_id, _ = get_from_empresa(self.id_empresa)
            connections.redis_client.delete(f"instances:{old_id_empresa}")
            connections.redis_client.delete(f"empresas:{old_instance_id}")
            redis_client_set(
                f"instances:{self.id_empresa}",
                json.dumps({"instance_id": instance_id, "token": token}),
                ex=8*60*60
            )
            redis_client_set(
                f"empresas:{instance_id}",
                json.dumps({"id_empresa": self.id_empresa, "token": token}),
                ex=8*60*60
            )

            table_id = f"{DB_SCHEMA}.instances"
            exists_instance = self._check_if_row_exists(
                table_id, "instance_id", instance_id, use_id_empresa=False
            )
            exists_empresa = self._check_if_row_exists(
                table_id, "id_empresa", self.id_empresa
            )

            conn = self._get_conn()
            cursor = conn.cursor()

            if exists_instance:
                query = f"UPDATE {table_id} SET instance_id = '', token = '' WHERE instance_id = %s"
                cursor.execute(query, (instance_id,))

            if exists_empresa:
                query = f"UPDATE {table_id} SET instance_id = %s, token = %s, id_empresa = %s WHERE id_empresa = %s"
                cursor.execute(
                    query, (instance_id, token, self.id_empresa, self.id_empresa)
                )
            else:
                query = f"INSERT INTO {table_id} (instance_id, token, id_empresa) VALUES (%s, %s, %s)"
                cursor.execute(query, (instance_id, token, self.id_empresa))

            conn.commit()
            cursor.close()

        except Exception as e:
            logger.error(f"Erro ao salvar dados da instância: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def save_responsible_data(
        self, email_responsavel_empresa, telefone_responsavel_empresa
    ) -> None:
        try:
            logger.info("Salvando dados do responsável...")

            redis_client_set(
                f"responsavel:{self.id_empresa}",
                json.dumps({"email": email_responsavel_empresa, "telefone": telefone_responsavel_empresa}),
                ex=8*60*60
            )

            table_id = f"{DB_SCHEMA}.contexto_academia"

            conn = self._get_conn()
            cursor = conn.cursor()

            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                query = f"UPDATE {table_id} SET email_responsavel_empresa = %s, telefone_responsavel_empresa = %s WHERE id_empresa = %s"
                cursor.execute(
                    query,
                    (
                        email_responsavel_empresa,
                        telefone_responsavel_empresa,
                        self.id_empresa,
                    ),
                )
            else:
                query = f"INSERT INTO {table_id} (id_empresa, email_responsavel_empresa, telefone_responsavel_empresa) VALUES (%s, %s, %s)"
                cursor.execute(
                    query,
                    (
                        self.id_empresa,
                        email_responsavel_empresa,
                        telefone_responsavel_empresa,
                    ),
                )

            conn.commit()
            cursor.close()

            logger.info("Dados do responsável salvos com sucesso.")

        except Exception as e:
            logger.error(f"Erro ao salvar dados do responsável: {e}")

    #NOTE: Refatorada para usar o postgres_client
    @log_func_time
    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def get_responsible_data(self) -> Tuple[Optional[str], Optional[str]]:
        """
        Retorna uma tupla (email, telefone) com os dados do responsável pela empresa.
        Busca no cache primeiro e, em caso de falha, no banco de dados PostgreSQL.
        """
        cache_key = f"responsavel:{self.id_empresa}"

        try:
            if cached_data := redis_client_get(cache_key):
                logger.info(f"Obtendo dados do responsável do cache para empresa {self.id_empresa}")
                responsible_data = json.loads(cached_data)
                return responsible_data.get("email"), responsible_data.get("telefone")
        except (json.JSONDecodeError, TypeError) as e:
            logger.warning(f"Não foi possível decodificar o cache para a chave {cache_key}: {e}")

        try:
            logger.info(f"Obtendo dados do responsável do PostgreSQL para empresa {self.id_empresa}")
            query = f"SELECT email_responsavel, telefone_responsavel FROM {DB_SCHEMA}.contexto_empresa"
            result = self._execute_query(query)
            result_data = result.to_dataframe()
            if result_data.empty:
                logger.warning(f"Nenhum dado de responsável encontrado para a empresa {self.id_empresa}")
                return None, None

            email = result_data['email_responsavel'].iloc[0]
            telefone = result_data['telefone_responsavel'].iloc[0]

            data_to_cache = {"email": email, "telefone": telefone}
            redis_client_set(
                cache_key,
                json.dumps(data_to_cache),
                ex=8 * 60 * 60 # 8 hrs
            )

            return email, telefone

        except Exception as e:
            logger.error(f"Erro ao obter dados do responsável para empresa {self.id_empresa}: {e}")
            return None, None

    @log_func_time
    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def save_pacto_data(self, login, senha):
        try:
            logger.info("Salvando dados do Pacto...")

            redis_client_set(
                f"pacto:{self.id_empresa}",
                json.dumps({"login": login, "senha": senha}),
                ex=8*60*60
            )

            table_id = f"{DB_SCHEMA}.pacto_users"

            conn = self._get_conn()
            cursor = conn.cursor()

            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                query = f"UPDATE {table_id} SET login = %s, senha = %s WHERE id_empresa = %s"
                cursor.execute(query, (login, senha, self.id_empresa))
            else:
                query = f"INSERT INTO {table_id} (login, senha, id_empresa) VALUES (%s, %s, %s)"
                cursor.execute(query, (login, senha, self.id_empresa))

            conn.commit()
            cursor.close()

        except Exception as e:
            logger.error(f"Erro ao salvar dados do Pacto: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def get_pacto_data(self) -> tuple[Optional[str], Optional[str]]:
        try:
            cache_pacto = redis_client_get(f"pacto:{self.id_empresa}")
            if cache_pacto:
                logger.info("Obtendo dados do Pacto do cache...")
                user_name = json.loads(cache_pacto).get("login")
                user_password = json.loads(cache_pacto).get("senha")
                return user_name, user_password

            logger.info("Obtendo dados do Pacto do PostgreSQL...")
            result = self._execute_query(
                f"SELECT login, senha FROM {DB_SCHEMA}.pacto_users"
            )
            result_data = result.to_dataframe()

            # IMPORTANTE: Decidir o fazer nesses casos aqui onde não acha pacto_user
            if result_data.empty:
                return None, None
            user_name = result_data['login'].iloc[0]
            user_password = result_data['senha'].iloc[0]
            redis_client_set(
                f"pacto:{self.id_empresa}",
                json.dumps({"login": user_name, "senha": user_password}),
                ex=8*60*60
            )

            return user_name, user_password

        except Exception as e:
            logger.error(f"Erro ao obter dados do Pacto: {e}")
            return None, None

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def save_chain_context(self, data) -> None:
        try:
            logger.info("Salvando dados da cadeia...")
            table_id = f"{DB_SCHEMA}.redes"
            data_json = json.dumps(data)
            data_atualizacao = Timestamp.now()

            conn = self._get_conn()
            cursor = conn.cursor()

            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                query = f"UPDATE {table_id} SET redes_json = %s, data_ultima_atualizacao = %s WHERE id_empresa = %s"
                cursor.execute(query, (Json(data), data_atualizacao, self.id_empresa))
            else:
                query = f"INSERT INTO {table_id} (redes_json, data_ultima_atualizacao, id_empresa) VALUES (%s, %s, %s)"
                cursor.execute(query, (Json(data), data_atualizacao, self.id_empresa))

            conn.commit()
            cursor.close()

            redis_client_set(
                f"chain_context-{self.id_empresa}",
                json.dumps(data),
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao salvar dados da cadeia: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def get_chain_context(self, extra_query="") -> list:
        try:
            cache_chain_context = redis_client_get(f"chain_context-{self.id_empresa}")
            if cache_chain_context:
                logger.info("Obtendo contexto da cadeia do cache...")
                return json.loads(cache_chain_context)

            logger.info("Obtendo contexto da cadeia do PostgreSQL...")
            result = self._execute_query(
                f"SELECT redes_json FROM {DB_SCHEMA}.redes", extra_query=extra_query
            )
            df = result.to_dataframe()

            if df.empty:
                logger.error("Query returned no results; cannot obtain chain context.")
                return []  # or return an empty list [] if that fits your use case

            chain_context = df.iloc[0]["redes_json"]

            if isinstance(chain_context, str):
                chain_context_data = chain_context
            else:
                chain_context_data = json.dumps(chain_context)

            redis_client_set(
                f"chain_context-{self.id_empresa}",
                chain_context,
                ex=8*60*60
            )

            return json.loads(chain_context_data)
        except Exception as e:
            logger.error(f"Erro ao obter contexto da cadeia: {e}")
            return []

    def save_model_source(self, model_source) -> None:
        try:
            redis_client_set(
                f"model_source:{self.id_empresa}",
                model_source,
                ex=8*60*60
            )

            conn = self._get_conn()
            cursor = conn.cursor()

            query = f"UPDATE {DB_SCHEMA}.contexto_academia SET model_source = %s WHERE id_empresa = %s"
            cursor.execute(query, (model_source, self.id_empresa))

            conn.commit()
            cursor.close()
        except Exception as e:
            logger.error(f"Erro ao salvar modelo: {e}")

    def get_model_source(self) -> str:
        try:
            if check_if_key_exists(f"model_source:{self.id_empresa}"):
                logger.info("Obtendo fonte do modelo do cache...")
                cache_model_source = redis_client_get(f"model_source:{self.id_empresa}")
                return cache_model_source.decode("utf-8")

            logger.info("Obtendo fonte do modelo do PostgreSQL...")
            query = f"SELECT model_source FROM {DB_SCHEMA}.contexto_academia WHERE id_empresa = %s"
            result = self._execute_query(query, params=[self.id_empresa])
            result_data = result.to_dataframe()
            model_source = result_data['model_source'].iloc[0]
            redis_client_set(
                f"model_source:{self.id_empresa}",
                str(model_source),
                ex=8*60*60
            )
            return model_source
        except Exception as e:
            logger.error(f"Erro ao obter fonte do modelo: {e}")
            return "openai"

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def get_last_messages(
        self, telefone, limit=6, roles_to_load_redis=["user", "assistant"]
    ) -> Optional[DataFrame]:
        telefone = str(telefone)
        try:
            if self.id_empresa is None:
                cache_last_messages = redis_client_get(
                    f"last_messages-{telefone}-{self.id_matriz}"
                )
            else:
                cache_last_messages = redis_client_get(
                    f"last_messages-{telefone}-{self.id_empresa}"
                )

            if cache_last_messages:
                json_last_messages = json.loads(cache_last_messages)
                if len(json_last_messages) >= limit:
                    json_last_messages = json_last_messages[-limit:]
                    redis_client_set(
                        f"last_messages-{telefone}-{self.id_empresa}",
                        json.dumps(json_last_messages),
                        ex=8*60*60
                    )
                return DataFrame(json_last_messages)

            logger.info("Obtendo últimas mensagens do PostgreSQL...")
            query = f"SELECT enviado_por, mensagem, model FROM {DB_SCHEMA}.conversas WHERE telefone = %s"
            params = [telefone]

            extra_query = " AND enviado_por IN ("
            for i, role in enumerate(roles_to_load_redis):
                if i > 0:
                    extra_query += ", "
                extra_query += "%s"
                params.append(role)
            extra_query += ")"

            if telefone:
                extra_query += " AND telefone = %s"
                params.append(telefone)

            extra_query += f" ORDER BY data_envio DESC LIMIT {limit}"

            result = self._execute_query(
                query, extra_query=extra_query, use_id_empresa=False, params=params
            )
            result_df = result.to_dataframe()
            result_df = result_df.iloc[::-1]

            if self.id_empresa is None:
                redis_client_set(
                    f"last_messages-{telefone}-{self.id_matriz}",
                    json.dumps(result_df.to_dict(orient='records')),
                    ex=8*60*60
                )
            else:
                redis_client_set(
                    f"last_messages-{telefone}-{self.id_empresa}",
                    json.dumps(result_df.to_dict(orient='records')),
                    ex=8*60*60
                )
            return result_df
        except Exception as e:
            logger.error(f"Erro ao obter últimas mensagens: {e}")
            return None

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def get_messages(self, telefone: str, id_conversa: str):
        """Obtém mensagens de uma conversa específica.

        Recomendação de índices para melhorar a performance:
        CREATE INDEX IF NOT EXISTS idx_conversas_telefone ON {DB_SCHEMA}.conversas (telefone);
        CREATE INDEX IF NOT EXISTS idx_conversas_id_conversa ON {DB_SCHEMA}.conversas (id_conversa);
        CREATE INDEX IF NOT EXISTS idx_conversas_data_envio ON {DB_SCHEMA}.conversas (data_envio);

        Um índice composto também pode ser útil:
        CREATE INDEX IF NOT EXISTS idx_conversas_telefone_id_conversa ON {DB_SCHEMA}.conversas (telefone, id_conversa);
        """
        try:
            # Otimizamos a query para selecionar apenas as colunas necessárias se conhecidas
            # Caso contrário, mantemos o SELECT * para compatibilidade
            query = f"""
                SELECT * FROM {DB_SCHEMA}.conversas 
                WHERE telefone = %s AND id_conversa = %s
                ORDER BY data_envio ASC
            """

            params = [telefone, id_conversa]

            conn = self._get_conn()
            cursor = conn.cursor(cursor_factory=DictCursor)

            # Definir cursor com nome permite processar grandes conjuntos de dados com pouco uso de memória
            cursor.execute(query, params)

            # Convertemos os resultados para DataFrame para manter compatibilidade
            columns = [desc[0] for desc in cursor.description]
            result_data = cursor.fetchall()
            cursor.close()

            if not result_data:
                return DataFrame(columns=columns)

            result_df = DataFrame(result_data, columns=columns)

            # Assegura que timestamps são tratados corretamente
            if "data_envio" in result_df.columns:
                result_df["data_envio"] = pd.to_datetime(
                    result_df["data_envio"], errors="coerce"
                )

            return result_df

        except Exception as e:
            logger.error(f"Erro ao obter mensagens: {e}")
            return (
                DataFrame()
            )  # Retorna DataFrame vazio em vez de None para consistência

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def save_connected_phone(self, telefone) -> None:
        try:
            logger.info("Verificando redis do connected_phone: {}".format(telefone))
            telefone_conectado = redis_client_get(f"connected_phone:{self.id_empresa}")
            empresa_telefone = redis_client_get(f"empresas_telefone:{telefone}")
            if telefone_conectado:
                if (
                    telefone_conectado.decode("utf-8") == telefone
                    and empresa_telefone.decode("utf-8") == self.id_empresa
                ):
                    logger.info("O que tá no redis é o mesmo")
                    return
            else:
                # Rotina de retirar telefones duplicados
                logger.info("Retirando telefones duplicados.")
                conn = self._get_conn()
                cursor = conn.cursor()

                duplicated_query = f"UPDATE {DB_SCHEMA}.contexto_academia SET telefone = NULL WHERE telefone = %s"
                cursor.execute(duplicated_query, (telefone,))

                logger.info("Indo buscar no PostgreSQL.")
                redis_client_set(
                    f"connected_phone:{self.id_empresa}",
                    telefone,
                    ex=8*60*60
                )
                redis_client_set(
                    f"empresas_telefone:{telefone}",
                    self.id_empresa,
                    ex=8*60*60
                )

                table_id = f"{DB_SCHEMA}.contexto_academia"
                if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                    query = f"UPDATE {table_id} SET telefone = %s WHERE id_empresa = %s"
                    cursor.execute(query, (telefone, self.id_empresa))
                else:
                    query = (
                        f"INSERT INTO {table_id} (telefone, id_empresa) VALUES (%s, %s)"
                    )
                    cursor.execute(query, (telefone, self.id_empresa))

                conn.commit()
                cursor.close()
        except Exception as e:
            logger.error(f"Erro ao salvar telefone conectado: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def get_connected_phone(self) -> Optional[str]:
        try:
            telefone_conectado = redis_client_get(f"connected_phone:{self.id_empresa}")
            if telefone_conectado:
                return telefone_conectado.decode("utf-8")

            query = f"SELECT telefone FROM {DB_SCHEMA}.contexto_academia"
            result = self._execute_query(query)
            result_data = result.to_dataframe()

            if result_data.empty:
                logger.info("Nenhum telefone conectado encontrado.")
                return None
            telefone = result_data['telefone'].iloc[0]
            redis_client_set(
                f"connected_phone:{self.id_empresa}",
                telefone,
                ex=8*60*60
            )
            return telefone
        except Exception as e:
            logger.error(f"Erro ao obter telefone conectado: {e}")
            return None

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def set_chain_status(self, status: bool = True) -> None:
        try:
            conn = self._get_conn()
            cursor = conn.cursor()

            query = (
                f"UPDATE {DB_SCHEMA}.instances SET is_rede = %s WHERE id_empresa = %s"
            )
            cursor.execute(query, (status, self.id_empresa))

            conn.commit()
            cursor.close()
        except Exception as e:
            logger.error(f"Erro ao salvar status da cadeia: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def save_message_status(self, data) -> None:
        try:
            logger.info(f"[save_message_status] {self.id_empresa} Atualizando status da mensagem")
            table_id = f"{DB_SCHEMA}.status_envios"

            id_mensagem = data.get("id", None)
            status_mensagem = data.get("status", None)
            provedor_mensagem = data.get("provedor_mensagem", "z_api")
            id_empresa = self.id_empresa
            is_group = data.get("isGroup", False)
            if not is_group:
                telefone = parse_phone(data.get("phone", None))
            else:
                telefone = data.get("phone", None)
            data_registro = Timestamp.now()

            conn = self._get_conn()
            cursor = conn.cursor()

            query = f"""
                INSERT INTO {table_id} 
                (id_mensagem, status_mensagem, provedor_mensagem, id_empresa, telefone, data_registro)
                VALUES (%s, %s, %s, %s, %s, %s)
            """

            cursor.execute(
                query,
                (
                    id_mensagem,
                    status_mensagem,
                    provedor_mensagem,
                    id_empresa,
                    telefone,
                    data_registro,
                ),
            )

            conn.commit()
            cursor.close()

            logger.info(f"[save_message_status] ({id_empresa}-{telefone}) Status da mensagem salvo com sucesso.")
        except Exception as e:
            logger.error(f"[save_message_status] ({id_empresa}-{telefone}) Erro ao salvar status da mensagem: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def save_conversation_analysis(self, phone, prompt, analysis, success) -> None:
        """Salva a análise de uma conversa."""
        logger.info("Salvando análise da conversa...")
        id_conversa = redis_client_get(
            f"current_conversation:{phone}-{self.id_empresa}"
        )
        if id_conversa:
            id_conversa = id_conversa.decode("utf-8")
        else:
            id_conversa = ""
        connections.redis_client.delete(
            f"current_conversation:{phone}-{self.id_empresa}"
        )
        try:
            conn = self._get_conn()
            cursor = conn.cursor()

            query = f"""
                INSERT INTO {DB_SCHEMA}.conversas_analise 
                (phone, analysis, success, id_empresa, id_conversa, data_analise, prompt)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """

            data_analise = Timestamp.now()
            prompt_json = Json(prompt) if isinstance(prompt, dict) else prompt

            cursor.execute(
                query,
                (
                    phone,
                    analysis,
                    success,
                    self.id_empresa,
                    id_conversa,
                    data_analise,
                    prompt_json,
                ),
            )

            conn.commit()
            cursor.close()

            logger.info("Análise da conversa salva com sucesso.")
        except Exception as e:
            logger.error("Erro ao salvar análise da conversa: %s", e)

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def save_notification_schema(
        self, descricao_original, schema_notificacao, notification_type, category=None
    ) -> None:
        """Salva o schema de notificações de uma empresa."""
        try:
            logger.info("Salvando schema de notificações...")
            table_id = f"{DB_SCHEMA}.notificacoes_schema"

            conn = self._get_conn()
            cursor = conn.cursor()

            # Prepare extra parameters for check
            extra_params = []
            if category:
                extra_query = " AND category = %s"
                extra_params.append(category)
            else:
                extra_query = ""

            if self._check_if_row_exists(
                table_id,
                "notification_type",
                notification_type,
                use_id_empresa=True,
                extra_query=extra_query,
            ):
                if category:
                    query = f"""
                        UPDATE {table_id}
                        SET schema_notificacao = %s,
                            descricao_original = %s,
                            data_envio = %s,
                            category = %s
                        WHERE id_empresa = %s AND notification_type = %s
                    """
                    data_envio = Timestamp.now()
                    cursor.execute(
                        query,
                        (
                            Json(schema_notificacao),
                            descricao_original,
                            data_envio,
                            category,
                            self.id_empresa,
                            notification_type,
                        ),
                    )
                else:
                    query = f"""
                        UPDATE {table_id}
                        SET schema_notificacao = %s,
                            descricao_original = %s,
                            data_envio = %s
                        WHERE id_empresa = %s AND notification_type = %s
                    """
                    data_envio = Timestamp.now()
                    cursor.execute(
                        query,
                        (
                            Json(schema_notificacao),
                            descricao_original,
                            data_envio,
                            self.id_empresa,
                            notification_type,
                        ),
                    )
            else:
                if category:
                    query = f"""
                        INSERT INTO {table_id} 
                        (id_empresa, schema_notificacao, descricao_original, data_envio, notification_type, category)
                        VALUES (%s, %s, %s, %s, %s, %s)
                    """
                    data_envio = Timestamp.now()
                    cursor.execute(
                        query,
                        (
                            self.id_empresa,
                            Json(schema_notificacao),
                            descricao_original,
                            data_envio,
                            notification_type,
                            category,
                        ),
                    )
                else:
                    query = f"""
                        INSERT INTO {table_id} 
                        (id_empresa, schema_notificacao, descricao_original, data_envio, notification_type)
                        VALUES (%s, %s, %s, %s, %s)
                    """
                    data_envio = Timestamp.now()
                    cursor.execute(
                        query,
                        (
                            self.id_empresa,
                            Json(schema_notificacao),
                            descricao_original,
                            data_envio,
                            notification_type,
                        ),
                    )

            conn.commit()
            cursor.close()

            cache_key = f"notification_scheme:{notification_type}:{self.id_empresa}"
            if category:
                cache_key += f":{category}"
            redis_client_set(
                cache_key,
                json.dumps(schema_notificacao),
                ex=8*60*60
            )
            logger.info(f"Esquema de notificação processado e armazenado com sucesso")
            logger.info("Schema de notificações salvo com sucesso.")

        except Exception as e:
            logger.error(f"Erro ao salvar schema de notificações: {e}")

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def get_notification_schema(
        self, notification_type, category=None
    ) -> Optional[Union[dict, list]]:
        """Obtém o schema de notificações de uma empresa."""
        try:
            cache_key = f"notification_scheme:{notification_type}:{self.id_empresa}"
            if category:
                cache_key += f":{category}"
            else:
                keys = connections.redis_client.keys(f"{cache_key}:*")
                if keys:
                    return [{
                        "category": key.decode().split(":")[-1],
                        **json.loads(connections.redis_client.get(key.decode()))
                    } for key in keys]

            cache_notification_schema = redis_client_get(cache_key)
            if cache_notification_schema:
                return json.loads(cache_notification_schema)

            logger.info("Obtendo schema de notificações do PostgreSQL...")
            table_id = f"{DB_SCHEMA}.notificacoes_schema"

            params = [notification_type]
            query = f"SELECT schema_notificacao, category FROM {table_id} WHERE notification_type = %s"

            if category:
                query += " AND category = %s"
                params.append(category)

            conn = self._get_conn()
            cursor = conn.cursor(cursor_factory=DictCursor)
            cursor.execute(query, params)

            results = cursor.fetchall()
            cursor.close()

            if not results:
                logger.warning(
                    f"Nenhum schema de notificações {notification_type} encontrado para a empresa: {self.id_empresa}"
                )
                return None

            if not category:
                result_dicts = []
                for row in results:
                    result_dicts.append(dict(row))

                for item in result_dicts:
                    if item.get("category") is None:
                        current_key = cache_key
                    else:
                        current_key = f"{cache_key}:{item.get('category')}"

                    schema_json = item.get("schema_notificacao")
                    if not isinstance(schema_json, str):
                        schema_json = json.dumps(schema_json)

                    redis_client_set(current_key, schema_json, ex=8*60*60)

                schema_notificacao = []
                for item in result_dicts:
                    schema_json = item.get("schema_notificacao")
                    if isinstance(schema_json, str):
                        schema_notificacao.append(json.loads(schema_json))
                    else:
                        schema_notificacao.append(schema_json)

                if len(schema_notificacao) == 1:
                    schema_notificacao = schema_notificacao[0]
            else:
                schema_json = results[0]["schema_notificacao"]

                if isinstance(schema_json, str):
                    schema_notificacao = json.loads(schema_json)
                else:
                    schema_notificacao = schema_json

                redis_client_set(
                    cache_key,
                    json.dumps(schema_notificacao),
                    ex=8*60*60
                )

            logger.info(
                "Schema de notificações encontrado. {}".format(
                    json.dumps(schema_notificacao, indent=2, ensure_ascii=False)
                )
            )

            return schema_notificacao

        except Exception as e:
            logger.error(f"Erro ao obter schema de notificações: {e}")
            return None

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def save_meta_diaria(
        self, id_conversa: str, phone: str, classification: str, action_description: str
    ) -> None:
        """Salva o resultado da meta diária."""
        id_conversa = redis_client_get(
            f"current_conversation:{phone}-{self.id_empresa}"
        )
        if id_conversa:
            id_conversa = id_conversa.decode("utf-8")
        else:
            id_conversa = ""
        try:
            conn = self._get_conn()
            cursor = conn.cursor()

            query = f"""
                INSERT INTO {DB_SCHEMA}.metas_diarias 
                (phone, classification, action_description, id_empresa, id_conversa, data_analise)
                VALUES (%s, %s, %s, %s, %s, %s)
            """

            data_analise = Timestamp.now()

            cursor.execute(
                query,
                (
                    phone,
                    classification,
                    action_description,
                    self.id_empresa,
                    id_conversa,
                    data_analise,
                ),
            )

            conn.commit()
            cursor.close()

            connections.redis_client.delete(f"meta_diaria:{id_conversa}")
            connections.redis_client.set(
                f"queue_free:{self.id_empresa}:{phone}", "True"
            )
        except Exception as e:
            logger.error("Erro ao salvar meta diária: %s", e)

    @trace_method(debug=DEBUG_MODE_TRACE_METHODS)
    def update_messager_channel(self, channel) -> None:
        try:
            logger.info("Atualizando canal de mensagens...")
            table_id = f"{DB_SCHEMA}.contexto_academia"
            data_atualizacao = Timestamp.now()

            conn = self._get_conn()
            cursor = conn.cursor()

            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                query = f"UPDATE {table_id} SET messager_channel = %s, data_ultima_atualizacao = %s WHERE id_empresa = %s"
                cursor.execute(query, (channel, data_atualizacao, self.id_empresa))
            else:
                query = f"INSERT INTO {table_id} (messager_channel, data_ultima_atualizacao, id_empresa) VALUES (%s, %s, %s)"
                cursor.execute(query, (channel, data_atualizacao, self.id_empresa))

            conn.commit()
            cursor.close()

            redis_client_set(
                f"messager_channel:{self.id_empresa}",
                channel,
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao atualizar canal de mensagens: {e}")

    def save_gymbot_token(self, token) -> None:
        try:
            logger.info("Atualizando token do GymBot...")
            table_id = f"{DB_SCHEMA}.gymbot_tokens"
            data_atualizacao = Timestamp.now()

            conn = self._get_conn()
            cursor = conn.cursor()

            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                query = f"UPDATE {table_id} SET token = %s, data_ultima_atualizacao = %s WHERE id_empresa = %s"
                cursor.execute(query, (token, data_atualizacao, self.id_empresa))
            else:
                query = f"INSERT INTO {table_id} (id_empresa, token, data_ultima_atualizacao) VALUES (%s, %s, %s)"
                cursor.execute(query, (self.id_empresa, token, data_atualizacao))

            conn.commit()
            cursor.close()

            redis_client_set(
                f"gymbot_token:{self.id_empresa}",
                token,
                ex=8*60*60
            )
        except Exception as e:
            logger.error(f"Erro ao atualizar token do GymBot no PostgreSQL: {e}")

    def get_gymbot_token(self) -> Optional[str]:
        try:
            if check_if_key_exists(f"gymbot_token:{self.id_empresa}"):
                logger.info("Obtendo token do GymBot do cache...")
                cache_token = redis_client_get(f"gymbot_token:{self.id_empresa}")
                return cache_token.decode("utf-8")

            logger.info("Obtendo token do GymBot do PostgreSQL...")
            table_id = f"{DB_SCHEMA}.gymbot_tokens"

            query = f"SELECT token FROM {table_id}"
            result = self._execute_query(query).to_dataframe()
            if result.empty:
                logger.warning("Nenhum token gymbot encontrado.")
                return None

            token = result["token"].iloc[0]
            return token
        except Exception as e:
            logger.error(f"Erro ao obter token do GymBot: {e}")
            return None

    def save_departament_descriptions(self, departments) -> None:
        logger.info("Salvando descrições dos departamentos...")
        data_atualizacao = Timestamp.now()
        table_id = f"{DB_SCHEMA}.gymbot_departamentos"

        conn = self._get_conn()
        cursor = conn.cursor()

        query_delete = f"DELETE FROM {table_id} WHERE id_empresa = %s"
        cursor.execute(query_delete, (self.id_empresa,))

        for departament in departments:
            logger.info(
                "Salvando descrição do departamento: {}".format(departament.get("name"))
            )
            try:
                exists_departament = self._check_if_row_exists(
                    table_id,
                    "id_departamento",
                    departament.get("id"),
                    use_id_empresa=True,
                )
                if exists_departament:
                    query = f"""
                        UPDATE {table_id} 
                        SET departamento = %s, 
                            descricao = %s, 
                            data_ultima_atualizacao = %s 
                        WHERE id_departamento = %s AND id_empresa = %s
                    """
                    cursor.execute(
                        query,
                        (
                            departament.get("name"),
                            departament.get("descricao"),
                            data_atualizacao,
                            departament.get("id"),
                            self.id_empresa,
                        ),
                    )
                else:
                    query = f"""
                        INSERT INTO {table_id} 
                        (departamento, descricao, id_departamento, id_empresa, data_ultima_atualizacao) 
                        VALUES (%s, %s, %s, %s, %s)
                    """
                    cursor.execute(
                        query,
                        (
                            departament.get("name"),
                            departament.get("descricao"),
                            departament.get("id"),
                            self.id_empresa,
                            data_atualizacao,
                        ),
                    )
            except Exception as e:
                logger.error(f"Erro ao salvar descrição do departamento: {e}")

        conn.commit()
        cursor.close()

        if not check_if_key_exists(f"gymbot:departament_descriptions:{self.id_empresa}"):
            logger.info("Salvando descrições dos departamentos no cache...")
            redis_client_set(
                f"gymbot:departament_descriptions:{self.id_empresa}",
                json.dumps(departments),
                ex=8*60*60
            )

    def delete_departament_descriptions(self, departments):
        """Deleta as descrições dos departamentos da empresa."""
        try:
            logger.info("Deletando descrições dos departamentos...")
            table_id = f"{DB_SCHEMA}.gymbot_departamentos"
            cache_departments = []
            has_cache = check_if_key_exists(f"gymbot:departament_descriptions:{self.id_empresa}")
            if has_cache:
                cache_departments = redis_client_get(f"gymbot:departament_descriptions:{self.id_empresa}").decode('utf-8')
                cache_departments = json.loads(cache_departments)

            for id_departamento in departments:
                query = f"DELETE FROM {table_id} WHERE id_departamento = '{id_departamento}'"
                self._execute_query(query)

                cache_departments = [dep for dep in cache_departments if dep.get('id_departamento') != id_departamento]

            if has_cache:
                redis_client_set(
                    f"gymbot:departament_descriptions:{self.id_empresa}",
                    json.dumps(cache_departments),
                    ex=8*60*60
                )

            logger.info("Descrições dos departamentos deletadas com sucesso.")
        except Exception as e:
            logger.error(f"Erro ao deletar descrições dos departamentos: {e}")

    def get_departament_descriptions(self) -> list:
        try:
            if check_if_key_exists(f"gymbot:departament_descriptions:{self.id_empresa}"):
                logger.info("Obtendo descrições dos departamentos do cache...")
                cache_departments = redis_client_get(
                    f"gymbot:departament_descriptions:{self.id_empresa}"
                )
                return json.loads(cache_departments)

            logger.info("Obtendo descrições dos departamentos do PostgreSQL...")
            table_id = f"{DB_SCHEMA}.gymbot_departamentos"
            query = f"SELECT id_departamento, departamento, descricao FROM {table_id}"
            result = self._execute_query(query).to_dataframe()

            if not result.empty:
                result["departamento"] = result["departamento"].str.lower()
                result = result[result["departamento"] != "conversasai"]

            if result.empty:
                logger.warning(
                    f"Nenhuma descrição encontrada para a empresa: {self.id_empresa}"
                )
                return []
            departament_descriptions = result[['id_departamento', 'departamento', 'descricao']].to_dict(orient='records')
            redis_client_set(
                f"gymbot:departament_descriptions:{self.id_empresa}",
                json.dumps(departament_descriptions),
                ex=8*60*60
            )
            return departament_descriptions

        except Exception as e:
            logger.error(f"Erro ao obter descrições dos departamentos: {e}")
            return []

    def save_auth_api_key(self, api_key, user, active) -> None:
        """
        Salva a chave de API no PostgreSQL e no Redis.
        *Sobre a compatibilidade com a chave unificada de antes:*
        - Se não for passado id_empresa:
            - A chave unificada é a única que existe.
            - Ela é a única que será usada para verificar se a chave já existe.
        - Se for passado id_empresa:
            - Será verificado se a chave já existe para a empresa.
            - Se existir, será atualizado o campo last_data.
            - Se não existir, será criada uma nova chave.

        Params:
            api_key (str): Chave de API a ser salva.
            user (str): Usuário associado à chave de API.
            active (bool): Indica se a chave está ativa ou não.
        """
        try:
            logger.info("Salvando chave de API no PostgreSQL...")

            use_id_empresa = self.id_empresa is not None

            table_id = f"{DB_SCHEMA}.api_keys"
            exists_api_key = self._check_if_row_exists(
                table_id, "api_key", api_key, use_id_empresa=use_id_empresa
            )
            exists_user = self._check_if_row_exists(
                table_id, "user", user, use_id_empresa=use_id_empresa
            )

            conn = self._get_conn()
            cursor = conn.cursor()

            if exists_api_key and exists_user:
                logger.info("Chave de API ja existe.")
                query = f"""
                    UPDATE {table_id}
                    SET last_data = CURRENT_TIMESTAMP
                    WHERE api_key = %s AND user = %s AND active = %s
                """

                params = [api_key, user, active]

                if self.id_empresa:
                    query += " AND id_empresa = %s"
                    params.append(self.id_empresa)

                cursor.execute(query, params)
            else:
                query = f"""
                    INSERT INTO {table_id} (api_key, user, creation_data, last_data, active, id_empresa)
                    VALUES (%s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, %s, %s)
                """
                cursor.execute(query, (api_key, user, active, self.id_empresa))

            conn.commit()
            cursor.close()

            logger.info("Chave de API salva com sucesso.")
            if active:
                redis_client_set(
                    f"api_keys:{self.id_empresa}:{api_key}",
                    json.dumps({"api_key": api_key, "user": user}),
                    ex=8*60*60
                )
            else:
                connections.redis_client.delete(f"api_keys:{self.id_empresa}:{api_key}")

        except Exception as e:
            logger.error(f"Erro ao salvar chave de API: {e}")

    def get_api_key_integration(
        self, api_key
    ) -> tuple[Union[str, bool], Union[str, bool]]:
        """Busca a chave de API e user."""
        try:
            conn = self._get_conn()
            cursor = conn.cursor()

            if not self.id_empresa:
                cache_api_key = redis_client_get(f"api_keys:{api_key}")
                if cache_api_key:
                    logger.info("Obtendo chave de API do cache...")
                    json_api_key: dict = json.loads(cache_api_key)
                    return json_api_key.get("api_key"), json_api_key.get("user")

                query = f"""
                SELECT api_key, user
                FROM {DB_SCHEMA}.api_keys
                WHERE api_key = %s
                """
                cursor.execute(query, (api_key,))
            else:
                cache_api_key = redis_client_get(
                    f"api_keys:{self.id_empresa}:{api_key}"
                )
                if cache_api_key:
                    logger.info("Obtendo chave de API do cache...")
                    json_api_key: dict = json.loads(cache_api_key)
                    return json_api_key.get("api_key"), json_api_key.get("user")

                query = f"""
                SELECT api_key, user 
                FROM {DB_SCHEMA}.api_keys 
                WHERE api_key = %s
                AND id_empresa = %s
                """
                cursor.execute(query, (api_key, self.id_empresa))

            row = cursor.fetchone()
            cursor.close()

            if not row:
                logger.warning(f"Nenhuma API Key encontrada para: {api_key}")
                return False, False

            api_key_value, user_value = row[0], row[1]
            logger.info(f"API Key encontrada: {api_key_value}, User: {user_value}")

            redis_client_set(
                f"api_keys:{api_key}",
                json.dumps({"api_key": api_key_value, "user": user_value}),
                ex=8*60*60
            )

            return api_key_value, user_value

        except Exception as e:
            logger.error(f"Erro ao buscar a chave de API no PostgreSQL: {e}")
            return False, False

    def save_auth_session(self, api_key, token, expiration_time) -> None:
        try:
            logger.info("Salvando sessão de autenticação no PostgreSQL...")
            table_id = f"{DB_SCHEMA}.sessions"
            # Timestamp is handled directly in the query

            conn = self._get_conn()
            cursor = conn.cursor()

            query = f"""
                INSERT INTO {table_id} (api_key, token, creation, expiration, id_empresa)
                VALUES (%s, %s, CURRENT_TIMESTAMP, %s, %s)
            """

            cursor.execute(query, (api_key, token, expiration_time, self.id_empresa))

            conn.commit()
            cursor.close()

            logger.info("Sessão de autenticação salva com sucesso.")
        except Exception as e:
            logger.error(f"Erro ao salvar sessão de autenticação: {e}")

    def get_messager_channel(self) -> str:
        try:
            logger.info("buscando canal de mensagens...")
            if check_if_key_exists(f"messager_channel:{self.id_empresa}"):
                logger.info("Obtendo canal de mensagens do cache...")
                cache_channel = redis_client_get(f"messager_channel:{self.id_empresa}")
                return cache_channel.decode("utf-8")

            logger.info("Obtendo canal de mensagens do PostgreSQL...")
            table_id = f"{DB_SCHEMA}.contexto_academia"
            query = f"SELECT messager_channel FROM {table_id}"
            result = self._execute_query(query).to_dataframe()
            if result.empty:
                logger.warning(
                    f"Nenhum canal de mensagens encontrado para a empresa: {self.id_empresa}, escolhendo z_api como padrão"
                )
                return "z_api"
            channel = result["messager_channel"].iloc[0]
            if not channel:
                return "z_api"
            redis_client_set(
                f"messager_channel:{self.id_empresa}",
                channel,
                ex=8*60*60
            )
            return channel
        except Exception as e:
            logger.error(f"Erro ao obter canal de mensagens: {e}")
            return "z_api"

    def delete_api_key(self, api_key) -> Optional[dict]:
        try:
            logger.info("Deletando chave de API no PostgreSQL...")
            table_id = f"{DB_SCHEMA}.api_keys"

            if not self.get_api_key_integration(api_key):
                logger.warning("Chave de API não encontrada para exclusão.")
                return {"message": "API key not found"}

            conn = self._get_conn()
            cursor = conn.cursor()

            if self.id_empresa:
                query = f"DELETE FROM {table_id} WHERE api_key = %s AND id_empresa = %s"
                cursor.execute(query, (api_key, self.id_empresa))
            else:
                query = f"DELETE FROM {table_id} WHERE api_key = %s"
                cursor.execute(query, (api_key,))

            conn.commit()
            cursor.close()

            logger.info("Chave de API deletada com sucesso.")
            if self.id_empresa:
                connections.redis_client.delete(f"api_keys:{self.id_empresa}:{api_key}")
            else:
                connections.redis_client.delete(f"api_keys:{api_key}")

        except Exception as e:
            logger.error(f"Erro ao deletar chave de API: {e}")

    def get_api_keys(self, name=None, page=1, limit=10) -> list:
        try:
            logger.info("Consultando chaves de API no PostgreSQL...")
            offset = (page - 1) * limit

            params = []
            query = f"SELECT api_key, user FROM {DB_SCHEMA}.api_keys"

            where_added = False

            if name:
                query += " WHERE LOWER(user) LIKE LOWER(%s)"
                params.append(f"%{name}%")
                where_added = True

            if self.id_empresa:
                if where_added:
                    query += " AND id_empresa = %s"
                else:
                    query += " WHERE id_empresa = %s"
                params.append(self.id_empresa)

            query += f" LIMIT {limit} OFFSET {offset}"

            conn = self._get_conn()
            cursor = conn.cursor()
            cursor.execute(query, params)

            results = cursor.fetchall()
            cursor.close()

            api_keys = [{"api_key": row[0], "user": row[1]} for row in results]
            logger.info(f"Encontradas {len(api_keys)} chaves de API.")
            return api_keys

        except Exception as e:
            logger.error(f"Erro ao consultar chaves de API: {e}")
            return []

    def save_memories(self, data: dict, phone: str) -> None:
        logger.info("Salvando memórias...")
        table_id = f"{DB_SCHEMA}.memories"
        data_json = json.dumps(data)
        data_atualizacao = Timestamp.now()

        conn = self._get_conn()
        cursor = conn.cursor()

        if self._check_if_row_exists(table_id, "phone", phone):
            query = f"UPDATE {table_id} SET memory = %s, data_ultima_atualizacao = %s WHERE phone = %s AND id_empresa = %s"
            cursor.execute(query, (data_json, data_atualizacao, phone, self.id_empresa))
        else:
            query = f"INSERT INTO {table_id} (phone, memory, id_empresa, data_ultima_atualizacao) VALUES (%s, %s, %s, %s)"
            cursor.execute(query, (phone, data_json, self.id_empresa, data_atualizacao))

        conn.commit()
        cursor.close()

        connections.redis_client.set(
            f"memories:{self.id_empresa}:{phone}",
            json.dumps(data),
            ex=8*60*60
        )

    def get_dados_bi(
        self,
        data_inicio: str,
        data_fim: str,
        type: str = Literal[
            "total_atendimentos",
            "frequencia_atendimento",
            "tempo_medio_atendimento",
        ],
    ):
        """Obtém dados de BI do PostgreSQL."""
        logger.info("Obtendo dados de BI...")
        with open(f"src/data/sql/bi/{type}.sql", "r") as file:
            query = file.read()

        # Convert BigQuery-specific SQL syntax to PostgreSQL
        query = query.replace("`", "")  # Remove backticks
        query = query.replace("TIMESTAMP(", "")  # Remove TIMESTAMP function wrapper
        query = query.replace(")", "", 1)  # Remove closing parenthesis from TIMESTAMP

        if query:
            query = query.format(
                dataset=DB_SCHEMA,
                id_empresa=self.id_empresa,
                data_inicio=data_inicio,
                data_fim=data_fim,
            )

        result = self._execute_query(query, use_id_empresa=False).to_dataframe()

        if result.empty:
            logger.warning(f"Nenhum dado encontrado para o tipo: {type}")
            return []

        return result

    def get_memories(self, phone: str) -> dict:
        logger.info("Obtendo memórias...")
        if check_if_key_exists(f"memories:{self.id_empresa}:{phone}"):
            logger.info("Obtendo memórias do cache...")
            cache_memories = redis_client_get(f"memories:{self.id_empresa}:{phone}")
            return json.loads(cache_memories)

        logger.info("Obtendo memórias do PostgreSQL...")
        query = f"SELECT memory FROM {DB_SCHEMA}.memories WHERE phone = %s"
        result = self._execute_query(query, params=[phone]).to_dataframe()
        if result.empty:
            logger.info("Nenhuma memória encontrada.")
            return {}

        memories = result["memory"].iloc[0]

        if isinstance(memories, str):
            memories_json = memories
        else:
            memories_json = json.dumps(memories)

        redis_client_set(
            f"memories:{self.id_empresa}:{phone}",
            memories_json,
            ex=8*60*60
        )
        return json.loads(memories_json)

    def register_log(self, data: Union[dict, list], table: str) -> None:
        """Registra logs em tabelas específicas.

        Recomendação de índices para melhorar a performance:
        CREATE INDEX IF NOT EXISTS idx_logs_{table}_request_time ON {DB_SCHEMA}.logs_{table} (request_time);
        CREATE INDEX IF NOT EXISTS idx_logs_{table}_id_empresa ON {DB_SCHEMA}.logs_{table} (id_empresa);

        Params:
            data (dict): Dados a serem registrados
            table (str): Nome da tabela de logs (sem o prefixo logs_)
        """
        logger.info(f"Registrando log de requisição na tabela {table}...")
        conn = None
        cursor = None

        try:
            table_id = f"{DB_SCHEMA}.logs_{table}"

            # Prepare columns and values for insert
            columns = list(data.keys())
            values = list(data.values())

            # Build the query - usando psycopg2.sql para evitar injeção SQL
            placeholders = sql.SQL(", ").join(sql.Placeholder() * len(columns))
            column_names = sql.SQL(", ").join(map(sql.Identifier, columns))

            query = sql.SQL("INSERT INTO {} ({}) VALUES ({})").format(
                sql.Identifier(f"{DB_SCHEMA}", f"logs_{table}"),
                column_names,
                placeholders,
            )

            conn = self._get_conn()
            cursor = conn.cursor()

            # Suporte a inserção em massa se data for uma lista
            if isinstance(data, list) and len(data) > 1:
                # Usando execute_values para inserção em massa eficiente
                columns = list(
                    data[0].keys()
                )  # Assumindo que todos os itens têm as mesmas chaves
                values = [[item[col] for col in columns] for item in data]
                execute_values(
                    cursor,
                    f"INSERT INTO {table_id} ({', '.join(columns)}) VALUES %s",
                    values,
                )
            else:
                cursor.execute(query, values)

            conn.commit()
            cursor.close()

            logger.info(f"Log de requisição registrado com sucesso na tabela {table}")

        except Exception as e:
            logger.error(f"Erro ao registrar log de requisição: {e}")

    def register_indicator(self, data):
        """Registra um indicador no BigQuery."""
        logger.info("Registrando indicador...")
        try:
            table_id = f"{GCP_BIGQUERY_DATASET}.indicadores"
            id_conversa = data.get("id_conversa", "")
            id_empresa = data.get("id_empresa", self.id_empresa)
            telefone = data.get("telefone", "")
            identificador = data.get("identificador", "")
            indicador = data.get("indicador", "")
            data_hora_evento = data.get("data_hora_evento", datetime.now())
            nome = data.get("nome", "")
            meta = data.get("meta", {})
            if meta == {}:
                meta = None

            query = f"""
                INSERT INTO `{table_id}` 
                (indicador, identificador, id_empresa, telefone, id_conversa, data_hora_evento, meta, nome)
                VALUES (@indicador, @identificador, @id_empresa, @telefone, @id_conversa, @data_hora_evento, @meta, @nome)
            """

            job_config = bigquery.QueryJobConfig(
                query_parameters=[
                    bigquery.ScalarQueryParameter("indicador", "STRING", indicador),
                    bigquery.ScalarQueryParameter("identificador", "STRING", identificador),
                    bigquery.ScalarQueryParameter("id_empresa", "STRING", str(id_empresa)),
                    bigquery.ScalarQueryParameter("telefone", "STRING", telefone),
                    bigquery.ScalarQueryParameter("id_conversa", "STRING", id_conversa),
                    bigquery.ScalarQueryParameter("data_hora_evento", "TIMESTAMP", data_hora_evento),
                    bigquery.ScalarQueryParameter("meta", "JSON", meta),
                    bigquery.ScalarQueryParameter("nome", "STRING", nome)
                ]
            )

            client = self._get_bigquery_client()
            client.query(query, job_config=job_config).result()
            logger.info("Indicador registrado com sucesso")
        except Exception as e:
            logger.error(f"Erro ao registrar indicador: {e}")

    def get_campaigns_context(self, id_campanha=None):
        """Obtém o contexto das campanhas.

        Recomendação de índices para melhorar a performance:
        CREATE INDEX IF NOT EXISTS idx_campanhas_id_empresa ON {DB_SCHEMA}.contexto_campanhas (id_empresa);
        CREATE INDEX IF NOT EXISTS idx_campanhas_id_campanha ON {DB_SCHEMA}.contexto_campanhas (id_campanha);
        CREATE INDEX IF NOT EXISTS idx_campanhas_data_inicio ON {DB_SCHEMA}.contexto_campanhas (data_inicio);

        Args:
            id_campanha: ID da campanha específica ou None para todas as campanhas

        Returns:
            Lista de dicionários com dados das campanhas
        """
        try:
            # Tenta obter do cache primeiro para maior performance
            cache_key = f"contexto_campanhas:{self.id_empresa}"
            if check_if_key_exists(cache_key):
                logger.info("Obtendo campanhas do cache...")
                cache_campaigns = redis_client_get(cache_key)
                campaigns_data = json.loads(cache_campaigns)

                # Filtra pelo ID se necessário
                if id_campanha:
                    filtered_campaigns = [
                        x for x in campaigns_data if x["id_campanha"] == id_campanha
                    ]
                    if filtered_campaigns:
                        return filtered_campaigns
                    # Se não encontrou no cache, continua para buscar no banco
                else:
                    return campaigns_data

            logger.info("Obtendo campanhas do PostgreSQL...")
            table_id = f"{DB_SCHEMA}.contexto_campanhas"

            # Usa funções nativas do PostgreSQL para formatação de data
            # TO_CHAR é mais eficiente que formatação no Python
            query = f"""
                SELECT 
                    id_empresa, 
                    id_campanha, 
                    nome, 
                    instrucao, 
                    keyword,
                    TO_CHAR(data_inicio, 'DD/MM/YYYY HH24:MI:SS') as data_inicio, 
                    TO_CHAR(data_fim, 'DD/MM/YYYY HH24:MI:SS') as data_fim,
                    imagem, 
                    is_template, 
                    TO_CHAR(data_atualizacao, 'DD/MM/YYYY HH24:MI:SS') as data_atualizacao,
                    whatsapp_link,
                    -- Adicionamos campos úteis para lógica de negócio
                    CASE 
                        WHEN data_fim IS NULL OR data_fim > CURRENT_TIMESTAMP THEN true 
                        ELSE false 
                    END as is_active
                FROM {table_id}
                WHERE id_empresa = %s
            """

            params = [self.id_empresa]

            # Adiciona filtro por id_campanha se fornecido
            if id_campanha:
                query += " AND id_campanha = %s"
                params.append(id_campanha)

            # Adiciona ordenação para resultados consistentes
            query += " ORDER BY data_inicio DESC NULLS LAST"

            conn = self._get_conn()
            cursor = conn.cursor(cursor_factory=DictCursor)
            cursor.execute(query, params)

            # Converte resultados para lista de dicionários
            results = cursor.fetchall()
            cursor.close()

            if not results:
                message = "Nenhuma campanha{} encontrada para a empresa: {}".format(
                    " com id " + id_campanha if id_campanha else "", self.id_empresa
                )
                logger.warning(message)
                return []

            # Converte resultados do cursor para lista de dicionários
            result_dict = [dict(row) for row in results]

            # Atualiza o cache apenas se buscou todas as campanhas
            if not id_campanha:
                redis_client_set(
                    cache_key,
                    json.dumps(result_dict),
                    ex=8*60*60
                )
            return result_dict
        except Exception as e:
            logger.error(f"Erro ao obter campanhas: {e}")
            return []  # Retorna lista vazia em vez de None para consistência

    def update_campanha_context(self, id_campanha, data) -> None:
        try:
            logger.info("Atualizando contexto da campanha...")
            table_id = f"{DB_SCHEMA}.contexto_campanhas"
            data_atualizacao = Timestamp.now()

            st_base64 = data.get("imagem")
            public_img_url = None
            if st_base64:
                bucket = Bucket(BUCKET_NAME_CAMPANHA)
                format_type, img_base64_str = st_base64.split("base64,")
                format_image = re.findall(r"(?<=data:)(.*)(?=;)", format_type)[0]
                base64_image = base64.b64decode(img_base64_str)
                public_img_url = bucket.upload(
                    base64_image,
                    f"{self.id_empresa}/{id_campanha}",
                    True,
                    True,
                    format_image,
                )

            data_inicio = get_date(data.get("data_inicio"), format="%d/%m/%Y %H:%M:%S")
            data_fim = (
                get_date(data.get("data_fim"), format="%d/%m/%Y %H:%M:%S") or None
            )

            connected_telefone = self.get_connected_phone()
            keyword = data.get("keyword", None)
            whatsapp_link = f"https://api.whatsapp.com/send?phone={connected_telefone}&text={urllib.parse.quote(keyword, safe='')}"

            campanhas = self.get_campaigns_context(None)
            campanha = [x for x in campanhas if x["id_campanha"] == id_campanha]

            conn = self._get_conn()
            cursor = conn.cursor()

            if campanha:
                query = f"""
                    UPDATE {table_id} SET
                      nome = %s, instrucao = %s, keyword = %s, data_inicio = %s, data_fim = %s, 
                      imagem = %s, is_template = %s, data_atualizacao = %s, whatsapp_link = %s
                    WHERE
                      id_empresa = %s AND id_campanha = %s
                """
                cursor.execute(
                    query,
                    (
                        data.get("nome"),
                        data.get("instrucao"),
                        keyword,
                        data_inicio,
                        data_fim,
                        public_img_url,
                        data.get("is_template"),
                        data_atualizacao,
                        whatsapp_link,
                        self.id_empresa,
                        id_campanha,
                    ),
                )
                is_update = True
            else:
                query = f"""
                    INSERT INTO {table_id} 
                      (id_empresa, id_campanha, nome, instrucao, keyword, data_inicio, data_fim, imagem, is_template, data_atualizacao, whatsapp_link)
                    VALUES
                      (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(
                    query,
                    (
                        self.id_empresa,
                        id_campanha,
                        data.get("nome"),
                        data.get("instrucao"),
                        keyword,
                        data_inicio,
                        data_fim,
                        public_img_url,
                        data.get("is_template"),
                        data_atualizacao,
                        whatsapp_link,
                    ),
                )
                is_update = False

            conn.commit()
            cursor.close()

            data.update(
                {
                    "id_empresa": self.id_empresa,
                    "id_campanha": id_campanha,
                    "imagem": public_img_url,
                    "data_atualizacao": datetime.strftime(
                        data_atualizacao, "%d/%m/%Y %H:%M:%S"
                    ),
                    "whatsapp_link": whatsapp_link,
                }
            )

            if check_if_key_exists(f"contexto_campanhas:{self.id_empresa}"):
                if is_update:
                    logger.info("Atualizando campanhas no cache...")
                    outras_campanhas = [
                        x for x in campanhas if x["id_campanha"] != id_campanha
                    ]
                    campanhas = outras_campanhas + [data]
                    redis_client_set(
                        f"contexto_campanhas:{self.id_empresa}",
                        json.dumps(campanhas),
                        ex=8*60*60
                    )
                    return

                logger.info("Inserindo campanhas no cache...")
                campanhas.append(data)
                redis_client_set(
                    f"contexto_campanhas:{self.id_empresa}",
                    json.dumps(campanhas),
                    ex=8*60*60
                )
                return
        except Exception as e:
            logger.error(f"Erro ao atualizar contexto da campanha: {e}")

    #NOTE: 
    @log_func_time
    def create_config(self, data: dict):
        """Cria um config no Postgres e no Redis."""
        try:
            table_id = f"{DB_SCHEMA}.configuration"
            data_atualizacao = Timestamp.now()

            if not self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                logger.info("Criando config...")
                logger.info("Data: %s", data)
                query = f"""
                INSERT INTO {table_id} (config, data_ultima_atualizacao, id_empresa)
                VALUES (:config, :data_ultima_atualizacao, :id_empresa)
                """
            else:
                logger.info(f"Config já existe para a empresa {self.id_empresa}. Atualizando...")
            # Query de UPDATE com sintaxe do PostgreSQL (:param_name)
                query = f"""
                    UPDATE {table_id}
                    SET config = :config,
                        data_ultima_atualizacao = :data_ultima_atualizacao
                    WHERE id_empresa = :id_empresa
                """ 
            params = {
                "config": json.dumps(data.get("config")),
                "data_ultima_atualizacao": data_atualizacao,
                "id_empresa": self.id_empresa,
            }
            logger.info("Query executed: %s", query)
            logger.info("Parameters used: %s", params)
            self._execute_query(query, params=params)
            redis_client_set(f"config:{self.id_empresa}", json.dumps(data))
            logger.info(f"Configuração para a empresa {self.id_empresa} salva com sucesso.")
        except Exception as e:
            logger.error(f"Erro ao criar/atualizar config para a empresa {self.id_empresa}: {e}")

    #NOTE: 
    @log_func_time
    def update_config(self, data):
        """Atualiza um config no Postgres e no Redis."""
        try:
            table_id = f"{DB_SCHEMA}.configuration"
            if self._check_if_row_exists(table_id, "id_empresa", self.id_empresa):
                logger.info("Atualizando config...")
                data_atualizacao = Timestamp.now()
                query = f"""
                    UPDATE {table_id}
                    SET config = :config,
                        data_ultima_atualizacao = :data_ultima_atualizacao
                    WHERE id_empresa = :id_empresa
                """
                params={
                    "config": json.dumps(data.get("config")),
                    "data_ultima_atualizacao": data_atualizacao,
                    "id_empresa": self.id_empresa,
                }
                self._execute_query(query, params=params)
                redis_client_set(f"config:{self.id_empresa}", json.dumps(data))
        except Exception as e:
            logger.error("Erro ao atualizar config: %s", e)

    #NOTE: 
    @log_func_time
    def get_config(self) -> dict:
        """Obtém um config do PostgreSQL ou do Redis."""
        try:
            cache_key = f"config:{self.id_empresa}"
            if check_if_key_exists(cache_key):
                config = redis_client_get(cache_key)
                return json.loads(config)
            
            logger.info(f"Obtendo config para a empresa {self.id_empresa} do PostgreSQL...")
            query = f"SELECT config FROM {DB_SCHEMA}.configuration WHERE id_empresa = %s"
            params = [self.id_empresa]
            result = self._execute_query(query, params=params).to_dataframe()

            if result.empty:
                return {}
                
            config = result.iloc[0]['config']
            redis_client_set(cache_key, config)
            return json.loads(config)
        except Exception as e:
            logger.error(f"Erro ao obter config para a empresa {self.id_empresa}: {e}")
            return {}

    #NOTE: 
    @log_func_time
    def delete_config(self):
        """Deleta um config do PostgreSQL e do Redis para uma empresa específica."""
        try:
            connections.redis_client.delete(f"config:{self.id_empresa}")
            logger.info(f"Deletando config da empresa {self.id_empresa} no PostgreSQL...")
            table_id = f'"{DB_SCHEMA}"."configuration"'
            query = f"DELETE FROM {table_id} WHERE id_empresa = :id_empresa"
            params = {"id_empresa": self.id_empresa}
            self._execute_query(query, params=params)
            logger.info(f"Config da empresa {self.id_empresa} deletado com sucesso.")
        except Exception as e:
            logger.error(f"Erro ao deletar config da empresa {self.id_empresa} {e}")
