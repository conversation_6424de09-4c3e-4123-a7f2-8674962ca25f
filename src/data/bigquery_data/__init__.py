import os
import logging
from typing import (
    Type,
    Union,
    Callable
)

from ._postgres_handler import PostgresDataHandler
from ._bigquery_handler import BigQueryDataHandler

from ._postgres_handler import (
    get_from_empresa as get_from_empresa_psql,
    get_from_instance as get_from_instance_psql,
    get_id_empresa as get_id_empresa_psql,
    is_rede as is_rede_psql,
    get_from_telefone as get_from_telefone_psql,
    check_if_key_exists as check_if_key_exists_psql,
    redis_client_get as redis_client_get_psql,
    redis_client_set as redis_client_set_psql,
)

from ._bigquery_handler import (
    get_from_empresa as get_from_empresa_bq,
    get_from_instance as get_from_instance_bq,
    get_id_empresa as get_id_empresa_bq,
    is_rede as is_rede_bq,
    get_from_telefone as get_from_telefone_bq,
    check_if_key_exists as check_if_key_exists_bq,
    redis_client_get as redis_client_get_bq,
    redis_client_set as redis_client_set_bq,
)


logger = logging.getLogger("conversas_logger")
db_type = os.getenv("DATABASE", "postgres").lower()

def data_handler_factory() -> Type[Union[PostgresDataHandler, BigQueryDataHandler]]:
    """
    Função para determinar e retornar a classe de manipulador de banco de dados apropriada.

    Esta função verifica a variável de ambiente "DATABASE" para decidir qual manipulador de banco de dados (PostgreSQL ou BigQuery) deve ser usado.

    :Return:
        Union[PostgresDataHandler, BigQueryDataHandler]: A classe de manipulador de dados selecionada.

    :Raises:
        ValueError: Se a variável de ambiente "DATABASE" não for "postgres" nem "bigquery".
    """
    
    logger.info(f"Configurando o handler do banco de dados para usar: {db_type}")

    if db_type == "postgres":
        return PostgresDataHandler
    elif db_type == "bigquery":
        return BigQueryDataHandler
    else:
        raise ValueError("Variável de ambiente DATABASE deve ser 'postgres' ou 'bigquery'")


BigQueryData = data_handler_factory()

logger.info(f"Configurando os métodos do handler do banco de dados para usar o DATABASE: {db_type}")

if db_type == "postgres":
    get_from_empresa: Callable = get_from_empresa_psql
    get_from_instance: Callable = get_from_instance_psql
    get_id_empresa: Callable = get_id_empresa_psql
    is_rede: Callable = is_rede_psql
    get_from_telefone: Callable = get_from_telefone_psql
    check_if_key_exists: Callable = check_if_key_exists_psql
    redis_client_get: Callable = redis_client_get_psql
    redis_client_set: Callable = redis_client_set_psql

elif db_type == "bigquery":
    get_from_empresa: Callable = get_from_empresa_bq
    get_from_instance: Callable = get_from_instance_bq
    get_id_empresa: Callable = get_id_empresa_bq
    is_rede: Callable = is_rede_bq
    get_from_telefone: Callable = get_from_telefone_bq
    check_if_key_exists: Callable = check_if_key_exists_bq
    redis_client_get: Callable = redis_client_get_bq
    redis_client_set: Callable = redis_client_set_bq