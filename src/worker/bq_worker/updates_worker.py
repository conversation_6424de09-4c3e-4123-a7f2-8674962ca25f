import logging
import json
import pandas as pd

from src.data.bigquery_data import BigQueryData as bq
from src.data.scraper import Scraper
from src.data.bigquery_data import redis_client_set
from src.extras.util import identify_voice, parse_phone, get_device_from_z_api, WorkersTracer
from src.integrations.gymbot.tools.integration_tools import GymbotIntegrationTools
import requests
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")

DOMAIN = os.getenv("DOMAIN", "https://api.conversas.ai")

@WorkersTracer(
    span_name_prefix=__name__,
    span_description="Update gym context",
    span_attributes={
        "id_empresa": "id_empresa",
    }
)
def salva_empresa_na_rede(id_empresa: str, data: dict, bq_: bq):
    # Tem que ter uma lógica pra pegar os dados relevantes da matriz e adicionar na tabela redes
    new_data = {
        "cep": data.get("cep", ""),
        "cidade": data.get("nomeCidade", ""),
        "endereco": data.get("endereco", ""),
        "estado": data.get("nomeEstado", ""),
        "id_empresa": id_empresa,
        "nome_empresa": data.get("nomeFantasia", None) or data.get("razaoSocial", None),
        "telefone": data.get("telComercial1", "")
    }
    
    current_data = bq_.get_chain_context() or []
    chaves = [x.get("id_empresa", None) for x in current_data]
    if id_empresa not in chaves:
        current_data.append(new_data)
    else:
        for i in range(len(current_data)):
            if current_data[i].get("id_empresa", None) == id_empresa:
                current_data[i] = new_data
    bq_.save_chain_context(current_data)

@WorkersTracer(
    span_name_prefix=__name__,
    span_description="Update gym context",
    span_attributes={
        "id_empresa": "id_empresa",
    }
)
def run(redis_client, max_iter=None):
    
    logger.info(" [*] Waiting for tasks")
    iter_count = 0
    while True:
        if max_iter and iter_count >= max_iter:
            break
        # Bloquear até receber uma nova tarefa
        try:
            task = redis_client.brpop('task_queue_bd_contexts')
            if task:
                task = json.loads(task[1].decode('utf-8'))
                logger.info(f"task: {json.dumps(task, indent=2, ensure_ascii=False)}")
                id_empresa = task['id_empresa']
                
                data = task['data']
                data = json.loads(json.dumps(data))

                bq_ = bq(id_empresa=id_empresa)
                type_ = task['type']
                match type_:
                    case 'gym':
                        logger.info(f"Atualizando contexto da academia {id_empresa}")
                        update_conn_data = True
                        isrede = data.get("rede", False)

                        if data.get("site", "") != "" and data.get("sitescraping", False):
                            scraper = Scraper(data['site'], recursive=True)
                            scraper.scrape()
                            if scraper.text != "":
                                data['site_texts'] = scraper.text


                        if isrede and data.get("matriz") is False and data.get("chaveMatriz", None) is not None:
                            update_conn_data = False
                            # Nesse caso é filial
                            id_matriz = data.get("chaveMatriz", None)
                            bq_.id_empresa = id_matriz # Para pegar os dados da matriz
                            salva_empresa_na_rede(id_empresa, data, bq_)
                            bq_.id_empresa = id_empresa # Para atualizar normalmente os dados da filial
            
                        if (data.get("zapiToken", "") not in ["", None]) and (data.get("zapiIdInstancia", "") not in ["", None]) and update_conn_data:
                            logger.info("Atualizando dados da Z API")
                            bq_.save_instance_data(data.get("zapiIdInstancia"), data.get("zapiToken"))
                            
                            if isrede and data.get("matriz") is True:
                                salva_empresa_na_rede(id_empresa, data, bq_)
                                bq_.set_chain_status()

                            phone = get_device_from_z_api(data.get("zapiIdInstancia"), data.get("zapiToken"))
                            print(phone)
                            
                            if phone:
                                phone = parse_phone(phone)
                                bq_.save_connected_phone(phone)

                        if (data.get("usuarioPactoLogin", "") != "") and (data.get("usuarioPactoSenha", "") != ""):
                            bq_.save_pacto_data(data.get("usuarioPactoLogin"), data.get("usuarioPactoSenha"))

                        if data.get("emailResponsavelConversasAI", "") != "" and data.get("telefoneResponsavelConversasAI", "") != "":
                            bq_.save_responsible_data(data.get("emailResponsavelConversasAI"), data.get("telefoneResponsavelConversasAI"))
                        
                        if data.get("telefone_conectado", "") != "":
                            telefone = parse_phone(data.get("telefone_conectado"))
                            bq_.save_connected_phone(telefone)

                        if data.get("habilitarIntegracaoGymbot", "") != "":
                            channel = "gym_bot"
                        else:
                            channel = "z_api"
                        bq_.update_messager_channel(channel)

                        bq_.update_gym_context(data)
                    
                    case 'gymbot_token':
                        saved = bq_.save_gymbot_token(data.get("token"))
                        save_webhook = data.get("save_webhook", True) and saved
                        webhook_enabled = data.get("webhook_enabled", True)
                        logger.info(f"type save webhook: {type(save_webhook)}")
                        logger.info(f"type webhook: {type(webhook_enabled)}")

                        gbit = GymbotIntegrationTools(id_empresa)

                        status_code_webhook, webhooks = gbit.get_webhooks()
                        if save_webhook and status_code_webhook == 200:
                            link_new_webhook = f"{DOMAIN}/gymbot_webhook?id_empresa={id_empresa}"
                            links_webhooks = [w["url"] for w in webhooks]
                            logger.info(f"[UPDATE] Webhooks encontrados: {links_webhooks}")
                            logger.info(f"[UPDATE] Webhook a ser adicionado: {link_new_webhook}")
                            if link_new_webhook not in links_webhooks:
                                gbit.create_webhook(
                                    events=["SESSION_COMPLETE", "MESSAGE_SENT", 
                                            "MESSAGE_RECEIVED", "MESSAGE_UPDATED"],
                                    name="ConversasAI",
                                    url_webhook=link_new_webhook,
                                    enabled=webhook_enabled
                                )
                            else:
                                logger.info(f"[UPDATE] Webhook já estava cadastrado")
                        else:
                            logger.error(f"Erro ao buscar webhooks da academia {id_empresa}")
                    
                    case 'gymbot_departament':
                        gbit = GymbotIntegrationTools(id_empresa)

                        depart_gbit = [{"name": d["name"], "id": d["id"]} for d in gbit.get_departamento()[1]]
                        depart_gbit = pd.DataFrame(depart_gbit)
                        depart_req = pd.DataFrame(data.get("departament_descriptions"))

                        depart_to_insert_or_update = depart_req.merge(depart_gbit, on="id", how="inner")
                        bq_.save_departament_descriptions(depart_to_insert_or_update[['id', 'descricao', 'name']].to_dict(orient='records'))

                    case 'gymbot_syncronize_departaments':
                        gbit = GymbotIntegrationTools(id_empresa)
                        depart_gbit = [{"name": d["name"], "id": d["id"]} for d in gbit.get_departamento()[1]]
                        depart_gbit = pd.DataFrame(depart_gbit)

                        depart_bq = pd.DataFrame(bq_.get_departament_descriptions(), columns=['id_departamento', 'departamento', 'descricao'])
                        depart_to_delete = depart_bq[~depart_bq['id_departamento'].isin(depart_gbit["id"])]

                        bq_.delete_departament_descriptions(depart_to_delete["id_departamento"].tolist())

                    case 'gymbot_departament_delete':
                        departaments = data.get("id", [])
                        bq_.delete_departament_descriptions(departaments)
                        
                    case 'plans':
                        logger.info(f"Atualizando contexto dos planos da academia {id_empresa}")

                        bq_.update_plans_context(data)

                    case 'classes':
                        logger.info(f"Atualizando contexto das turmas da academia {id_empresa}")

                        bq_.update_classes_context(data)

                    case 'phases':
                        logger.info(f"Atualizando contexto das fases da academia {id_empresa}")

                        bq_.update_phases_context(data)

                    case 'personality':
                        logger.info(f"Atualizando contexto da personalidade da academia {id_empresa}")

                        bq_.update_personality_context(data)

                        data = identify_voice(str(data.get("personalidade", "Voz feminina sempre.")), bq_)

                        redis_client_set(f"voice_schedule:{id_empresa}", json.dumps(data))

                    case 'products':
                        logger.info(f"Atualizando contexto dos produtos da academia {id_empresa}")

                        bq_.update_products_context(data)
                    
                    case 'user':
                        logger.info(f"Atualizando contexto do usuário da academia {id_empresa}")

                        telefone = data.get("telefone", "")
                        fase = data.get("fase", "")
                        contexto = data.get("contexto", "")
                        origin_last_update = data.get("origin_last_update", None)
                        id_matriz = task.get('id_matriz', None)
                        bq_.id_matriz = id_matriz                    

                        bq_.save_user_context(contexto, telefone, fase, origin=origin_last_update)

                    case 'z_api':
                        logger.info(f"Atualizando dados da Z API da academia {id_empresa}")

                        bq_.save_instance_data(data.get('instance_id'), data.get("token"))
                        redis_client_set(
                            f"instances:{id_empresa}",
                            json.dumps(data),
                            ex=8*60*60
                        )

                        phone = get_device_from_z_api(data.get("zapiIdInstancia"), data.get("zapiToken"))

                        if phone:
                            phone = parse_phone(phone)
                            bq_.save_connected_phone(phone)
                            redis_client_set(
                                f"connected_phone:{id_empresa}",
                                phone,
                                ex=8*60*60
                            )

                    case 'user_empresa':
                        logger.info(f"Atualizando empresa do usuario para {id_empresa}")

                        telefone = data.get("telefone", "")
                        fase = data.get("fase", "")
                        contexto = data.get("contexto", "")
                        new_id_empresa = data.get("new_empresa", None)
                        id_matriz = task['id_matriz']
                        bq_.id_matriz = id_matriz 
                        bq_.save_user_empresa(new_id_empresa, contexto, telefone, fase)

                    case 'memory':
                        logger.info(f"Atualizando memória da academia {id_empresa}")
                        telefone = data.get("telefone", "")
                        memoria = data.get("memoria", "")
                        bq_.save_memories(memoria, telefone)

                    case 'api-v2':
                        action = task['action']
                        resource = task['resource']

                        logger.info(f"Efetuando ação {action} no recurso {resource} da academia {id_empresa}")

                        attribute_name = f"{action}_{resource}"

                        if hasattr(bq_, attribute_name):
                            method = getattr(bq_, attribute_name)
                            try:
                                method(data)
                            except TypeError:
                                method()
                        else:
                            raise NotImplementedError(f"O método {action} não foi implementado para o recurso {resource}")

                    case 'campanha':
                        id_campanha = task.get("id_campanha", None)
                        logger.info(f"Atualizando contexto da campanha {id_campanha or 'nova'} da academia {id_empresa}")
                        bq_.update_campanha_context(id_campanha, data)

                logger.info(" [x] Done")

        except Exception as e:
            logger.error(f" [x] Error: {e}")
            iter_count += 1
            continue
        iter_count += 1
