import os
import logging

from src.data.bigquery_data import BigQueryData as bq, get_id_empresa
from src.integrations.pacto.tools.integration_tools import PactoIntegrationTools as PIT
from src.connections.connections import Connections
from src.extras.util import WorkersTracer

GCP_BIGQUERY_DATASET = os.getenv("GCP_BIGQUERY_DATASET")
DB_SCHEMA = os.getenv("DB_SCHEMA", "public")
BUCKET_NAME_CAMPANHA = os.getenv("BUCKET_NAME_CAMPANHA", "image_campanha")
if DB_SCHEMA == "development":
    BUCKET_NAME_CAMPANHA += "_dev"

logger = logging.getLogger("conversas_logger")
CONNECTIONS = Connections.get_instance()

@WorkersTracer(
    span_name_prefix=__name__,
    span_description="Atualização de contexto",
    span_attributes={
        "dataset": DB_SCHEMA,
        "id_empresa": "id_empresa"
    }
)
def update_empresas(id_empresas: list) -> None:
    logger.info("Atualizando empresas...")
    for id_empresa in id_empresas:
        try:
            bq_ = bq(id_empresa)
            pit_ = PIT(id_empresa)
            recent_context = pit_.get_empresas_context()
            if recent_context:
                bq_.update_empresa(recent_context)
                logger.info(f"Empresa {id_empresa} atualizada com sucesso.")
            else:
                logger.warning(
                    f"Empresa {id_empresa} não possui contexto válido.")
        except Exception as e:
            logger.error(f"Erro ao atualizar empresa {id_empresa}: {e}")

@WorkersTracer(
    span_name_prefix=__name__,
    span_description="Atualização de planos",
    span_attributes={
        "dataset": DB_SCHEMA,
        "id_empresa": "id_empresa"
    }
)
def update_planos(id_empresas: list) -> None:
    logger.info("Atualizando planos...")
    for id_empresa in id_empresas:
        try:
            bq_ = bq(id_empresa)
            pit_ = PIT(id_empresa)
            recent_context = pit_.get_planos_context()
            if recent_context:
                bq_.update_empresa(recent_context)
                logger.info(f"Empresa {id_empresa} atualizada com sucesso.")
            else:
                logger.warning(
                    f"Empresa {id_empresa} não possui contexto de planos válido.")
        except Exception as e:
            logger.error(f"Erro ao atualizar empresa {id_empresa}: {e}")

@WorkersTracer(
    span_name_prefix=__name__,
    span_description="Atualização de turmas",
    span_attributes={
        "dataset": DB_SCHEMA,
        "id_empresa": "id_empresa"
    }
)
def update_classes(id_empresas: list) -> None:
    logger.info("Atualizando turmas...")
    for id_empresa in id_empresas:
        try:
            bq_ = bq(id_empresa)
            pit_ = PIT(id_empresa)
            recent_context = pit_.get_turmas_context()
            if recent_context:
                bq_.update_empresa(recent_context)
                logger.info(f"Empresa {id_empresa} atualizada com sucesso.")
            else:
                logger.warning(
                    f"Empresa {id_empresa} não possui contexto de turmas válido.")
        except Exception as e:
            logger.error(f"Erro ao atualizar empresa {id_empresa}: {e}")

@WorkersTracer(
    span_name_prefix=__name__,
    span_description="Inicia atualização de contexto",
    span_attributes={
        "dataset": DB_SCHEMA,
        "id_empresa": "id_empresa"
    }
)
def run() -> None:
    can_run = CONNECTIONS.redis_client.get("updates:can_run")
    if not can_run:
        CONNECTIONS.redis_client.set("updates:can_run", "True")
        can_run = "True"
    else:
        can_run = can_run.decode("utf-8")
    if can_run != "True":
        logger.info("Atualização já em execução.")
        return
    CONNECTIONS.redis_client.set("updates:can_run", "False")

    logger.info("Iniciando atualização de contexto...")
    id_empresas = get_id_empresa()
    if id_empresas:
        update_empresas(id_empresas)
        update_planos(id_empresas)
        update_classes(id_empresas)
        logger.info("Atualização de contexto concluída.")
    else:
        logger.warning("Nenhuma empresa encontrada para atualizar.")

    CONNECTIONS.redis_client.set("updates:can_run", "True")
