from flask import Blueprint, request, jsonify, current_app
from src.api.app.limiter.limiter import limiter
import json
import os

from src.data.bigquery_data import BigQueryData as bq
from src.api.app.auth.utils.auth_wrappers import authentication_required
from src.api.app.exception.exception import BadRequest
from src.extras.util import parse_phone, get_uuid, get_date, RoutesTracing
import uuid
from pandas import Timestamp

GCP_BIGQUERY_DATASET = os.getenv("GCP_BIGQUERY_DATASET")
DB_SCHEMA = os.getenv("DB_SCHEMA", "public")
BUCKET_NAME_CAMPANHA = os.getenv("BUCKET_NAME_CAMPANHA", "image_campanha")
if DB_SCHEMA == "development":
    BUCKET_NAME_CAMPANHA += "_dev"

atualizar_contexto_academia_bp = Blueprint('atualizar_contexto_academia', __name__)
atualizar_contexto_planos_bp = Blueprint('atualizar_contexto_planos', __name__)
atualizar_contexto_fases_bp = Blueprint('atualizar_contexto_fases', __name__)
atualizar_contexto_turmas_bp = Blueprint('atualizar_contexto_turmas', __name__)
atualizar_contexto_produtos_bp = Blueprint('atualizar_contexto_produtos', __name__)
atualizar_contexto_personalidade_bp = Blueprint('atualizar_contexto_personalidade', __name__)
atualizar_contexto_usuario_bp = Blueprint('atualizar_contexto_usuario', __name__)
atualizar_dados_z_api_bp = Blueprint('atualizar_dados_z_api', __name__)
atualizar_messager_channel_bp = Blueprint('atualizar_messager_channel', __name__)
atualizar_contexto_campanha_bp = Blueprint('atualizar_contexto_campanha', __name__)

@atualizar_contexto_academia_bp.route('', methods=['POST'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="atualizar_contexto_academia",
    capture_body_fields=["type", "id_empresa"],
    capture_query_params=["empresa"],
)
def atualizar_contexto_academia(id_empresa: str = None):
    """
    Esta rota serve para atualizar o contexto da academia, esses dados são usados 
    para guiar a IA sobre informações específicas da empresa.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: body
        in: body
        required: true
        schema:
          id: contexto_academia
          required:
            - nomeFantasia
            - endereco
            - cidade
            - estado
            - cep
            - cnpj
            - site
            - email
            - telComercial1
            - permiteContratosConcomitantes
          properties:
            nomeFantasia:
                type: string
                description: Nome fantasia da academia
                example: Academia Exemplo
            razaoSocial:
                type: string
                description: Razão social da academia
                example: Academia Exemplo Ltda.
            endereco:
                type: string
                description: Endereço da academia
                example: Rua Exemplo, 123
            cidade:
                type: string
                description: Cidade da academia
                example: Cidade Exemplo
            estado:
                type: string
                description: Estado da academia
                example: Estado Exemplo
            cep:
                type: string
                description: CEP da academia
                example: 12345-678
            complemento:
                type: string
                description: Complemento do endereço da academia
                example: Apto 101
            numero:
                type: string
                description: Número do endereço da academia
                example: 123
            setor:
                type: string
                description: Setor da academia
                example: Setor Exemplo
            cnpj:
                type: string
                description: CNPJ da academia
                example: 12.345.678/0001-99
            site:
                type: string
                description: Site da academia
                example: https://www.academiaexemplo.com
            email:
                type: string
                description: Email da academia
                example: <EMAIL>
            telComercial1:
                type: string
                description: Telefone comercial da academia
                example: (11) 1234-5678
            telefone_conectado:
                type: string
                description: Telefone conectado no z-api ou gymbot
                example: "+5562912341234"
            permiteContratosConcomitantes:
                type: boolean
                description: Permite contratos concomitantes
                example: true
            urlLojaVendaOnline:
                type: string
                description: URL da loja de venda online
                example: https://www.academiaexemplo.com/loja
            urlPlanosLojaVendaOnline:
                type: string
                description: URL dos planos na loja de venda online
                example: https://www.academiaexemplo.com/planos
            urlProdutosLojaVendaOnline:
                type: string
                description: URL dos produtos na loja de venda online
                example: https://www.academiaexemplo.com/produtos
            urlAgendaAulasLojaVendaOnline:
                type: string
                description: URL da agenda de aulas na loja de venda online
                example: https://www.academiaexemplo.com/agenda
            horario_funcionamento:
                type: string
                description: Horário de funcionamento da academia
                example: 'Seg-Sex: 6h-22h, Sáb: 8h-18h, Dom: 8h-14h'
            proposito:
                type: string
                description: Propósito da academia
                example: Promover saúde e bem-estar
            site_texts:
                type: string
                description: Informações adicionais sobre a academia
                example: Informações adicionais sobre a academia.
            gymbot_token:
                type: string
                description: Token do GymBot
                example: pn_xxxxxxxxxxxxxxxxx
            matriz:
                type: boolean
                description: Indica se a academia é matriz
                example: true
            chaveMatriz:
                type: string
                description: Chave da matriz
                example: empresa-1
            desabilitarAulaExperimental:
                type: boolean
                description: Indica se aula experimental é ativada
                example: false
            
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
                type: object
                properties:
                    success:
                        type: string
                        example: success
        400:
            description: ID da academia não informado
            schema:
                id: error
                type: object
                properties:
                    error:
                        type: string
                        example: ID da academia não informado
    """
    data = request.json
    id_empresa = request.args.get('empresa')

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    if not data:
        raise BadRequest("Dados não informados")

    task = {
        "type" : "gym",
        "id_empresa" : id_empresa,
        "data" : data
    }

    current_app.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))

    return jsonify({"success": "success"}), 200


@atualizar_contexto_planos_bp.route('', methods=['POST'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="atualizar_contexto_planos",
    capture_body_fields=["type", "id_empresa"],
    capture_query_params=["empresa"],
)
def atualizar_contexto_planos(id_empresa: str = None):
    """
    Esta rota serve para atualizar o contexto dos planos, esses dados servem 
    para que a IA saiba quais são os planos disponíveis.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: body
        in: body
        required: true
        schema:
          id: contexto_planos
          required:
              - planosComVendaOnline
              - planosComVendaConsultor
          properties:
            planosComVendaOnline:
              type: array
              description: Planos com link de venda online
              items:
                  type: object
                  properties:
                      descricao:
                          type: string
                          description: Descrição do plano
                          example: Plano Básico
                      urlVendaOnline:
                          type: string
                          description: URL de venda online do plano
                          example: https://example.com/compra/plano-basico
                      condicaoPagamento:
                          type: string
                          description: Condição de pagamento do plano
                          example: Mensal
                      descricaoEncantamento:
                          type: string
                          description: Descrição encantamento do plano
                          example: Acesso ilimitado à academia
                      modalidades:
                          type: array
                          description: Modalidades do plano
                          items:
                              type: object
                              properties:
                                  modalidade:
                                      type: string
                                      description: Modalidade do plano
                                      example: Musculação
                      valorMensal:
                          type: number
                          description: Valor mensal do plano
                          example: 99.90
                      taxaAdesao:
                          type: number
                          description: Taxa de adesão do plano
                          example: 50.00
                      valorAnuidade:
                          type: number
                          description: Valor da anuidade do plano
                          example: 599.00
                      mesAnuidade:
                          type: string
                          description: Mês da anuidade do plano
                          example: Janeiro
                      diaAnuidade:
                          type: string
                          description: Dia da anuidade do plano
                          example: 15
                      anuidadeNaParcela:
                          type: boolean
                          description: Anuidade na parcela do plano
                          example: true
                      valorTotalDoPlano:
                          type: number
                          description: Valor total do plano
                          example: 1199.00
                      duracaoPlano:
                          type: string
                          description: Duração do plano
                          example: 12 meses
                      quantidadeDiasExtra:
                          type: number
                          description: Quantidade de dias extra do plano
                          example: 30
                      planoPersonal:
                          type: boolean
                          description: Plano personal do plano
                          example: true
                      regimeRecorrencia:
                          type: boolean
                          description: Regime de recorrência do plano
                          example: true
                      renovavelAutomaticamente:
                          type: boolean
                          description: Renovável automaticamente do plano
                          example: true
                      maximoVezesParcelar:
                          type: number
                          description: Máximo de vezes para parcelar do plano
                          example: 12
                      categorias:
                          type: array
                          description: Categorias do plano
                          items:
                              type: string
                              example: Fitness
            planosComVendaConsultor:
                type: array
                description: Planos com venda por consultor
                items:
                    type: object
                    properties:
                        descricao:
                            type: string
                            description: Descrição do plano
                            example: Plano Premium
                        percentualmultacancelamento:
                            type: number
                            description: Percentual multa cancelamento do plano
                            example: 10
                        recorrencia:
                            type: boolean
                            description: Recorrência do plano
                            example: true
                        permitiracessosomentenaempresavendeucontrato:
                            type: boolean
                            description: Permitir acesso somente na empresa que vendeu o contrato do plano
                            example: false
                        planopersonal:
                            type: boolean
                            description: Plano personal do plano
                            example: true
                        permitepagarcomboleto:
                            type: boolean
                            description: Permite pagar com boleto do plano
                            example: true
                        convidadospormes:
                            type: number
                            description: Convidados por mês do plano
                            example: 2
                        valordescontoboletopagantecipado:
                            type: number
                            description: Valor desconto boleto pagante cipado do plano
                            example: 10.00
                        porcentagemdescontoboletopagantecipado:
                            type: number
                            description: Porcentagem desconto boleto pagante cipado do plano
                            example: 5.0
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: ID da academia não informado
            schema:
                id: error
    """
    data = request.json
    id_empresa = request.args.get('empresa')

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    if not data:
        raise BadRequest("Dados não informados")

    task = {
        "type" : "plans",
        "id_empresa" : id_empresa,
        "data" : data
    }

    current_app.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))

    return jsonify({"success": "success"}), 200


@atualizar_contexto_fases_bp.route('', methods=['POST'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="atualizar_contexto_fases",
    capture_body_fields=["type", "id_empresa"],
    capture_query_params=["empresa"],
)
def atualizar_contexto_fases(id_empresa: str = None):
    """
    Esta rota serve para atualizar o contexto das fases, esses dados servem 
    para que a IA saiba como falar com o usuário em cada fase.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: body
        in: body
        required: true
        schema:
          id: contexto_fases
          required:
            - content
          properties:
            content:
                type: array
                description: Fases do usuário
                items:
                    type: object
                    properties:
                        codigo:
                            type: string
                            description: Código da fase
                            example: 1
                        name:
                            type: string
                            description: Nome da fase
                            example: INICIAL
                        descricao:
                            type: string
                            description: Descrição da fase
                            example: Fase inicial
                        instrucao_ia:
                            type: string
                            description: Instrução da IA
                            example: Olá, seja bem-vindo à academia!
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: ID da academia não informado
            schema:
                id: error
    """
    data = request.json
    if isinstance(data, dict) and data.get("content", {}) != {}:
        data = data.get("content", {})

    id_empresa = request.args.get('empresa')

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    if not data:
        raise BadRequest("Dados não informados")

    task = {
        "type" : "phases",
        "id_empresa" : id_empresa,
        "data" : data
    }

    current_app.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))

    return jsonify({"success": "success"}), 200


@atualizar_contexto_turmas_bp.route('', methods=['POST'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="atualizar_contexto_turmas",
    capture_body_fields=["type", "id_empresa"],
    capture_query_params=["empresa"],
)
def atualizar_contexto_turmas(id_empresa: str = None):
    """
    Esta rota serve para atualizar o contexto das turmas, esses dados servem 
    para que a IA saiba quais são as turmas disponíveis.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: body
        in: body
        required: true
        schema:
          id: contexto_turmas
          required:
            - content
          properties:
            content:
                type: array
                description: Turmas da empresa
                items:
                    type: object
                    properties:
                        codigo:
                            type: string
                            description: Código da turma
                            example: 1
                        idademaxima:
                            type: number
                            description: Idade máxima da turma
                            example: 99
                        idademinima:
                            type: number
                            description: Idade mínima da turma
                            example: 18
                        datafinalvigencia:
                            type: string
                            description: Data final de vigência da turma
                            example: 2022-12-31
                        datainicialvigencia:
                            type: string
                            description: Data inicial de vigência da turma
                            example: 2022-01-01
                        modalidade:
                            type: string
                            description: Modalidade da turma
                            example: Musculação
                        descricao:
                            type: string
                            description: Descrição da turma
                            example: Turma de musculação
                        minutosantecedenciamarcaraula:
                            type: number
                            description: Minutos de antecedência para marcar aula
                            example: 30
                        minutosantecedenciadesmarcaraula:
                            type: number
                            description: Minutos de antecedência para desmarcar aula
                            example: 30
                        aulacoletiva:
                            type: boolean
                            description: Aula coletiva
                            example: true
                        ocupacao:
                            type: number
                            description: Ocupação da turma
                            example: 10
                        mensagem:
                            type: string
                            description: Mensagem da turma
                            example: Mensagem da turma
                        dias:
                            type: string
                            description: Dias da semana da turma
                            example: SG;TR;QA;QI
                        horarios:
                            type: string
                            description: Horários da turma
                            example: '08:00 - 09:00;11:00 - 12:00;14:00 - 15:00'
                        permitiraulaexperimental:
                            type: boolean
                            description: Permitir aula experimental
                            example: true
                        permitirdesmarcarreposicoes:
                            type: boolean
                            description: Permitir desmarcar reposições
                            example: true
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: ID da academia não informado
            schema:
                id: error
    """
    data = request.json
    if isinstance(data, dict) and data.get("content", {}) != {}:
        data = data.get("content", {})

    id_empresa = request.args.get('empresa')

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    if not data:
        raise BadRequest("Dados não informados")

    task = {
        "type" : "classes",
        "id_empresa" : id_empresa,
        "data" : data
    }

    current_app.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))
    
    return jsonify({"success": "success"}), 200


@atualizar_contexto_produtos_bp.route('', methods=['POST'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="atualizar_contexto_produtos",
    capture_body_fields=["type", "id_empresa"],
    capture_query_params=["empresa"],
)
def atualizar_contexto_produtos(id_empresa: str = None):
    """
    Esta rota serve para atualizar o contexto dos produtos, esses dados servem 
    para que a IA saiba quais são os produtos disponíveis.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: body
        in: body
        required: true
        schema:
          id: contexto_produtos
          required:
            - content
          properties:
            content:
                type: array
                description: Produtos da empresa
                items:
                    type: object
                    properties:
                        codigo:
                            type: string
                            description: Código do produto
                            example: 1
                        descricao:
                            type: string
                            description: Descrição do produto
                            example: Produto 1
                        valorfinal:
                            type: number
                            description: Valor final do produto
                            example: 99.90
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: ID da academia não informado
            schema:
                id: error
    """
    data = request.json
    if isinstance(data, dict) and data.get("content", {}) != {}:
        data = data.get("content", {})

    id_empresa = request.args.get('empresa')

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    if not data:
        raise BadRequest("Dados não informados")

    task = {
        "type" : "products",
        "id_empresa" : id_empresa,
        "data" : data
    }

    current_app.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))
    
    return jsonify({"success": "success"}), 200

@atualizar_contexto_personalidade_bp.route('', methods=['POST'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="atualizar_contexto_personalidade",
    capture_body_fields=["type", "id_empresa"],
    capture_query_params=["empresa"],
)
def atualizar_contexto_personalidade(id_empresa: str = None):
    """
    Esta rota serve para atualizar o contexto da personalidade, esse dado serve 
    para que a IA saiba como falar com o usuário.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: body
        in: body
        required: true
        schema:
          id: contexto_personalidade
          required:
            - content
          properties:
            content:
                type: object
                description: Personalidade da empresa
                properties:
                    personalidade:
                        type: string
                        description: Personalidade da empresa
                        example: amigável
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: ID da academia não informado
            schema:
                id: error
    """
    data = request.json
    if isinstance(data, dict) and data.get("content", {}) != {}:
        data = data.get("content", {})
    id_empresa = request.args.get('empresa')

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    if not data:
        raise BadRequest("Dados não informados")

    task = {
        "type" : "personality",
        "id_empresa" : id_empresa,
        "data" : data
    }

    current_app.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))
    
    return jsonify({"success": "success"}), 200

@atualizar_contexto_usuario_bp.route('/', methods=['POST'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="atualizar_contexto_usuario",
    capture_body_fields=["type", "id_empresa"],
    capture_query_params=["empresa"],
)
def atualizar_contexto_usuario(id_empresa: str = None):
    """
    Esta rota serve para atualizar o contexto do usuário, esses dados servem para que a IA saiba como falar com o usuário.
    ---
    tags:
        - Contexto
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: telefone
        in: query
        type: string
        default: 5511999999999
        required: true
        description: Telefone do usuário
      - name: body
        in: body
        required: true
        schema:
          id: enviar_mensagem

    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: ID da academia não informado
            schema:
                id: error
    """
    id_empresa = request.args.get('empresa')

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    data = request.json

    if not data:
        raise BadRequest("Dados não informados")

    phone = request.args.get('telefone')

    if not phone:
        raise BadRequest("Telefone não informado")

    try:
        phone = parse_phone(phone)
    except (AttributeError, IndexError) as e:
        raise BadRequest("Telefone inválido") from e

    task = {
        "type": "user",
        "id_empresa": id_empresa,
        "data":
        {
            "telefone": phone,
            "contexto": json.dumps(data),
            "fase": data.get("fase_crm", "LEADS_HOJE"),
            "origin_last_update": "api"
        }
    }

    current_app.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))

    return jsonify({"success": "success"}), 200

@atualizar_dados_z_api_bp.route('', methods=['POST'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="atualizar_dados_z_api",
    capture_body_fields=["type", "id_empresa"],
    capture_query_params=["empresa"],
)
def atualizar_dados_z_api(id_empresa: str = None):
    """
    Esta rota serve para atualizar os dados da API Z, esses dados servem 
    para que o sistema relacione determinada instância com a empresa.
    ---
    tags:
        - Whatsapp
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: body
        in: body
        required: true
        schema:
          id: dados_z_api
          required:
            - token
            - instance_id
          properties:
            token:
                type: string
                description: Token da API Z
                example: 1234567890
            instance_id:
                type: string
                description: ID da instância da API Z
                example: 1234567890
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: ID da academia não informado
            schema:
                id: error
    """
    data = request.json
    id_empresa = request.args.get('empresa')

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    if not data:
        raise BadRequest("Dados não informados")

    task = {
        "type" : "z_api",
        "id_empresa" : id_empresa,
        "data" : data
    }

    instance_id = task["data"].get("instance_id")
    token = task["data"].get("token")

    if instance_id is None or str(instance_id).strip() == "":
        return jsonify({"error": "Instance ID não informado."}), 400

    if token is None or str(token).strip() == "":
        return jsonify({"error": "Token não informado."}), 400

    current_app.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))

    return jsonify({"success": "success"}), 200

@atualizar_messager_channel_bp.route('/', methods=['POST'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="atualizar_messager_channel",
    capture_body_fields=["type", "id_empresa"],
    capture_query_params=["empresa"],
)
def atualizar_messager_channel(id_empresa: str = None):
    """
    Essa rota atualiza o canal de mensagem da empresa z_api ou gym_bot
    ---
    tags:
        - Whatsapp
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: body
        in: body
        required: true
        schema:
          id: messager_channel
          required:
            - channel
          properties:
            channel:
                type: string
                description: Qual API do whatsapp será usada? (gym_bot ou z_api)
                example: z_api
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: ID da academia não informado
            schema:
                id: error
    """
    data = request.json

    task = {
        "type" : "messager_channel",
        "id_empresa" : id_empresa,
        "data" : data
    }

    current_app.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))
    
    return jsonify({"success": "success"}), 200


@atualizar_contexto_campanha_bp.route('', methods=['POST'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="atualizar_contexto_campanha",
    capture_body_fields=["type", "id_empresa"],
    capture_query_params=["empresa"],
)
def atualizar_contexto_campanha(id_empresa: str = None):
    """
    Esta rota serve para atualizar o contexto das campanhas, esses dados servem 
    para que a IA saiba quais são as campanhas disponíveis.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: id_campanha
        in: query
        type: string
        description: ID da campanha, se não for informado ou não existir no banco, será criada uma nova campanha
        example: dd9ef247-8500-4c56-9be7-c38f24c7a853a
      - name: body
        in: body
        required: true
        schema:
            id: contexto_campanha
            required:
                - nome
                - instrucao
                - keyword
            properties:
                nome:
                    type: string
                    description: Nome da campanha
                    example: dia das mães
                instrucao:
                    type: string
                    description: instrução da campanha
                    example: Mande uma linda mensagem de dia das mães para o aluno.
                keyword:
                    type: string
                    description: gatilho para campanha (hashtag)
                    example: diadasmaes
                data_inicio:
                    type: string
                    description: data de início da campanha no formato dd/mm/yyyy hh:mm:ss
                    example: 01/01/2025 00:00:00
                data_fim:
                    type: string
                    description: data de fim da campanha no formato dd/mm/yyyy hh:mm:ss
                    example: 31/01/2025 00:00:00
                imagem:
                    type: string
                    description: base64 da imagem da campanha
                    example: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAABjElEQVRIS+2Vv0oDQRDG
                is_template:
                    type: bool
                    description: Diz se o campo instrução é um template fixo ou é usado pela IA
                    example: false
    responses:
        202:
            description: Retorna sucesso
            schema:
                id: success
                type: object
                properties:
                    success:
                        type: string
                        example: success
                    details: 
                        type: string
                        example: Campanha será processada
        400:
            description: ID da academia não informado
            schema:
                id: error
                type: object
                properties:
                    error:
                        type: string
                        example: ID da academia não informado
    
    """
    data = request.json
    id_empresa = request.args.get('empresa')
    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    if not data:
        raise BadRequest("Dados não informados")
    
    id_campanha = get_uuid(request.args.get('id_campanha'))
    details = f"campanha foi {'criada' if not id_campanha else 'atualizada'} com sucesso."
    id_campanha = id_campanha or str(uuid.uuid4())

   
    data['data_inicio'] = (
        get_date(data.get("data_inicio"), format="%d/%m/%Y %H:%M:%S") or 
        get_date(data.get("data_inicio"), format="%d/%m/%Y") or
        Timestamp.now()
    ).strftime("%d/%m/%Y %H:%M:%S")

    try:
        data['data_fim'] = (
            get_date(data.get("data_fim"), format="%d/%m/%Y %H:%M:%S") or 
            get_date(data.get("data_fim"), format="%d/%m/%Y") or 
            None
        ).strftime("%d/%m/%Y %H:%M:%S")
    except (AttributeError, ValueError):
        data['data_fim'] = None

    bq_ = bq(id_empresa=id_empresa)
    bq_.update_campanha_context(id_campanha, data)

    details += (
        f"Campanha programada para iniciar em: {str(data.get('data_inicio'))} e "
        f"termina em:" + f" {str(data.get('data_fim')) if data.get('data_fim') != 'None' else ' sem data de término'}"
    )

    return jsonify({"success": "success", "details": details}), 200
