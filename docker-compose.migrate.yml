services:
  migration-runner:
    container_name: migration-runner-task
    build:
      context: src/migration_service
      dockerfile: Dockerfile
    environment:
      GCP_BIGQUERY_DATASET: "development" 
      GOOGLE_APPLICATION_CREDENTIALS: "src/connections/conversas-ai.json"
      POSTGRES_HOST: "postgres"
      POSTGRES_PORT: "5432"
      POSTGRES_DB: "orion"
      POSTGRES_USER: "postgres"
      POSTGRES_PASSWORD: "postgres"
      DB_SCHEMA: "conversas_ai"
      MIGRATE_LIMIT: "100"
      MIGRATION_DO_TRUNCATE: "true"
    networks:
      - orion_default

networks:
  orion_default:
    external: true